import React, { PropsWithChildren, useEffect, useState } from "react";
import { sharedState } from "@src/shared-state/shared-state";
import { Redirect } from "expo-router";
import { Routes } from "@src/navigation/constants";

export const AuthProvider = ({ children }: PropsWithChildren) => {
  // Shared Data
  const user = sharedState.user.use();
  const adminUser = sharedState.superAdmin.use();
  const appReadyState = sharedState.appReadyState.use();

  // Hooks
  /** Logic for `isAuthenticated`:
   * - `isAuthenticated` is undefined when the app is first loaded
   * - it is only set to true/false once `sharedState` and `firebase` are loaded and validated
   * */
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>();

  useEffect(() => {
    if (appReadyState?.isReady && !appReadyState.notReadyMessage) {
      if (!user && !adminUser) {
        setIsAuthenticated(false);
      } else {
        setIsAuthenticated(true);
      }
    }
  }, [appReadyState?.isReady, appReadyState?.notReadyMessage]);

  return (
    <>
      {isAuthenticated === false && (
        /** Intentionally checking for boolean false inorder to wait for `firebase` and `sharedState` initialization */
        <Redirect href={Routes.LOGIN} />
      )}
      {children}
    </>
  );
};
