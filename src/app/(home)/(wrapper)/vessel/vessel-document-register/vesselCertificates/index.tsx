import React from "react";
import { VesselCertificates } from "@src/pages/VesselDocumentRegister/VesselCertificates";
import { useVesselDocumentSubNav } from "@src/hooks/useSubNav";
import { sharedState } from "@src/shared-state/shared-state";
import { Routes } from "@src/navigation/constants";

const VesselCertificatesPage = () => {
  const vesselId = sharedState.vesselId.use();

  return (
    <VesselCertificates
      headerSubNavigation={useVesselDocumentSubNav(
        vesselId,
        Routes.VESSEL_CERTIFICATES
      )}
    />
  );
};

export default VesselCertificatesPage;
