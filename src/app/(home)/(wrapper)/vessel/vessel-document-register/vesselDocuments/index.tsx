import React from "react";
import { VesselDocuments } from "@src/pages/VesselDocumentRegister/VesselDocuments";
import { sharedState } from "@src/shared-state/shared-state";
import { Routes } from "@src/navigation/constants";
import { useVesselDocumentSubNav } from "@src/hooks/useSubNav";

const VesselDocumentsPage = () => {
  const vesselId = sharedState.vesselId.use();

  return (
    <VesselDocuments
      headerSubNavigation={useVesselDocumentSubNav(
        vesselId,
        Routes.VESSEL_DOCUMENTS
      )}
    />
  );
};

export default VesselDocumentsPage;
