import { createStyleSheet, useStyles } from "@src/theme/styles";
import React, { useEffect, useState } from "react";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { Redirect } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { sharedState } from "@src/shared-state/shared-state";

export default function Index() {
  // Styles
  const { styles } = useStyles(styleSheet);

  // Shared Data
  const user = sharedState.user.use();
  const adminUser = sharedState.superAdmin.use();
  const appReadyState = sharedState.appReadyState.use();

  // Hooks
  /** Logic for `isAuthenticated`:
   * - `isAuthenticated` is undefined when the app is first loaded
   * - it is only set to true/false once `sharedState` and `firebase` are loaded and validated
   * */
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>();

  useEffect(() => {
    if (appReadyState?.isReady && !appReadyState.notReadyMessage) {
      if (!user && !adminUser) {
        setIsAuthenticated(false);
      } else {
        // If the user / admin user is loaded, then check if they are allowed to auto login
        if (isAllowedToAutoLogin()) {
          setIsAuthenticated(true);
          return;
        }

        // Force a login
        setIsAuthenticated(false);
      }
    }
  }, [appReadyState?.isReady, appReadyState?.notReadyMessage]);

  // Check if user can autologin
  const isAllowedToAutoLogin = () => {
    if (user) {
      if (
        user.isLoginDisabled ||
        user.isDeactivated ||
        user.state !== "active"
      ) {
        // Check if superAdmin
        return !!adminUser;
      }
    }
    return true;
  };

  return (
    <>
      {/** Probably show the splash screen here */}
      <SeaStack
        direction={"column"}
        gap={40}
        align={"center"}
        justify={"center"}
        style={styles.container}
      >
        <SeaIcon icon={"directions_boat_filled"} size={40} />
        <SeaTypography variant={"title"}>
          Sea Flux - Splash Screen Image{" "}
        </SeaTypography>
        {isAuthenticated === true && (
          <Redirect href={getRoutePath(Routes.FLEET_DASHBOARD)} />
        )}
        {isAuthenticated === false && (
          /** Intentionally checking for boolean false inorder to wait for `firebase` and `sharedState` initialization */
          <Redirect href={Routes.LOGIN} />
        )}
      </SeaStack>
    </>
  );
}

const styleSheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    alignItems: "center",
  },
}));
