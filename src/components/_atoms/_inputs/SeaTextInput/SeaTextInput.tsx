import React, { ReactElement } from "react";
import {
  Platform,
  TextInput,
  TextInputProps,
  TextStyle,
  View,
  ViewStyle,
} from "react-native";
import Animated from "react-native-reanimated";
import SeaIcon from "@src/components/_legacy/SeaIcon/SeaIcon";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { useBounceOnFocus } from "@src/components/_atoms/_animations/useBounceOnFocus";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import { ErrorText } from "@src/components/_atoms/_inputs/ErrorText/ErrorText";

const SINGLE_LINE_HEIGHT = 40;
const MULTI_LINE_HEIGHT = 144;

export interface SeaInputProps extends TextInputProps {
  value: string;
  onChangeText?: (value: string) => void;
  label?: string;
  multiLine?: boolean;
  placeholderText?: string;
  icon?: ReactElement<typeof SeaIcon>;
  style?: ViewStyle;
  inputContainerStyle?: ViewStyle;
  hasError?: boolean;
  errorText?: string;
  noValidation?: boolean;
}

export const SeaTextInput = ({
  value,
  onChangeText,
  label,
  multiLine,
  placeholderText,
  icon,
  style,
  inputContainerStyle,
  hasError = false,
  errorText,
  noValidation = false,
  ...otherProps
}: SeaInputProps) => {
  const { styles, theme } = useStyles(styleSheet);
  const platformOverrides = Platform.select({
    web: { outline: "none" } as TextStyle,
  });

  return (
    <View style={[styles.container, style]}>
      {label && (
        <SeaTypography variant={"label"}>{label.toUpperCase()}</SeaTypography>
      )}

      <View
        style={[
          styles.inputContainer,
          { height: multiLine ? MULTI_LINE_HEIGHT : SINGLE_LINE_HEIGHT },
          inputContainerStyle,
          hasError && styles.inputContainerError,
        ]}
      >
        {icon && <View style={{ marginRight: 5 }}>{icon}</View>}
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholderText ?? label}
          multiline={multiLine}
          placeholderTextColor={theme.colors.text.placeholder}
          {...otherProps}
          style={[
            styles.textInputStyle,
            { height: multiLine ? MULTI_LINE_HEIGHT - 6 : SINGLE_LINE_HEIGHT },
            platformOverrides,
          ]}
        />
      </View>

      {!noValidation ? (
        <ErrorText hasError={hasError} text={errorText} />
      ) : (
        <></>
      )}
    </View>
  );
};

const styleSheet = createStyleSheet((theme) => ({
  container: {
    width: "100%",
  },
  labelContainer: {},
  inputContainer: {
    borderRadius: 8,
    borderWidth: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    height: SINGLE_LINE_HEIGHT,
    width: "100%",
    backgroundColor: theme.colors.white,
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
    borderColor: theme.colors.borderColor,
  },
  inputContainerError: {
    borderColor: theme.colors.status.errorPrimary,
  },
  textInputStyle: {
    height: SINGLE_LINE_HEIGHT,
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 14,
    lineHeight: 24,
    fontWeight: 400,
    width: "100%",
    color: theme.colors.text.input,
    paddingVertical: 8,
  },
}));
