import React, { useState } from "react";
import {
  CheckBoxActions,
  SeaSelectModal,
  SimpleSelectionData,
  TabularSelectionData,
} from "@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal";
import { StyleProp, TouchableOpacity, View, ViewStyle } from "react-native";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import { ErrorText } from "@src/components/_atoms/_inputs/ErrorText/ErrorText";

const ROW_HEIGHT = 40;
interface SeaSelectInputProps<T> {
  disabled?: boolean;
  label?: string;
  data: SimpleSelectionData | TabularSelectionData[];
  onItemSelect?: (action: CheckBoxActions, changedValue: string) => void;
  onSetItems?: (action: CheckBoxActions, changedValues: string[]) => void;
  selectedItemValues?: string[];
  primaryActionOnPress?: () => void;
  style?: StyleProp<ViewStyle>;
  isMulti?: boolean;
  showSelectAllOption?: boolean;
  showSearch?: boolean;
  inputTextProp?: string;
  textInputStyle?: StyleProp<ViewStyle>;
  modalTitle?: string;
  hasError?: boolean;
  errorText?: string;
  noValidation?: boolean;
}

export const SeaSelectInput = <T,>({
  data,
  disabled = false,
  // TODO: Change this to be `FALSE` by default
  isMulti = true,
  inputTextProp,
  label = "",
  modalTitle,
  onItemSelect,
  onSetItems,
  primaryActionOnPress,
  showSelectAllOption = true,
  showSearch = false,
  selectedItemValues = [],
  hasError = false,
  errorText,
  style,
  textInputStyle,
  noValidation = false,
}: SeaSelectInputProps<T>) => {
  // Hooks
  const [viewModal, setViewModal] = useState(false);
  const { styles } = useStyles(styleSheet);

  // Input Text field's value
  const inputText =
    inputTextProp ?? getDefaultInputText(data, selectedItemValues);

  const closeModal = () => setViewModal(false);

  const modalTitleText = modalTitle ?? label;

  return (
    <>
      <View style={[styles.container, style]}>
        {!!label && (
          <SeaTypography variant={"label"}>{label.toUpperCase()}</SeaTypography>
        )}
        <View style={[styles.dropdownContainer, disabled && { opacity: 0.5 }]}>
          <TouchableOpacity
            style={{ height: ROW_HEIGHT, overflow: "hidden" }}
            onPress={() => setViewModal(!viewModal)}
            disabled={disabled}
          >
            <SeaStack
              justify={"between"}
              style={[
                styles.stack,
                hasError && styles.stackError,
                textInputStyle,
              ]}
            >
              <SeaTypography variant={"input"}>
                {inputText ?? "Select an Item"}
              </SeaTypography>
              <SeaIcon icon={viewModal ? "arrow_drop_up" : "arrow_drop_down"} />
            </SeaStack>
          </TouchableOpacity>

          {/* Multi select - Modal Popup */}
          {isMulti ? (
            <SeaSelectModal
              title={modalTitleText}
              data={data}
              selectedItemValues={selectedItemValues}
              visible={viewModal}
              onClose={closeModal}
              onItemSelect={onItemSelect}
              primaryActionLabel={"OK"}
              primaryActionOnPress={primaryActionOnPress}
              onSetItems={onSetItems}
              showSelectAllOption={showSelectAllOption}
              showSearch={showSearch}
            />
          ) : (
            <SeaSelectModal
              title={modalTitleText}
              data={data}
              selectedItemValues={selectedItemValues}
              visible={viewModal}
              onClose={closeModal}
              onItemSelect={(action, changedValue) => {
                // Update the entire item list instead of the single item
                onSetItems && onSetItems(action, [changedValue]);
                closeModal();
              }}
              showPrimaryAction={false}
              showSelectAllOption={false}
              showSearch={showSearch}
            />
          )}
        </View>

        {!noValidation ? (
          <ErrorText hasError={hasError} text={errorText} />
        ) : (
          <></>
        )}
      </View>
    </>
  );
};

const getDefaultInputText = (
  data: SimpleSelectionData | TabularSelectionData[],
  selectedItemValues?: string[],
) => {
  return data
    ?.map((dataItem) => {
      // TODO: Cater for TabularSelectionData
      if (selectedItemValues?.includes(dataItem.value)) {
        return dataItem.label ?? dataItem.value;
      }
      return undefined;
    })
    .filter((a) => a !== undefined)
    .join(", ");
};

const styleSheet = createStyleSheet((theme) => ({
  container: { flex: 1 },
  dropdownContainer: {},
  stack: {
    height: ROW_HEIGHT,
    borderRadius: 8,
    borderWidth: 1,
    width: "100%",
    borderColor: theme.colors.borderColor,
    backgroundColor: theme.colors.input.background,
    minWidth: 100,
    paddingHorizontal: 16,
  },
  stackError: {
    borderColor: theme.colors.status.errorPrimary,
  },
}));
