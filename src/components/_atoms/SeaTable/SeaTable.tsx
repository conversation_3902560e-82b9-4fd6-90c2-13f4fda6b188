import React, {
  Dispatch,
  ReactElement,
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from "react";
import {
  StyleProp,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
  Pressable,
  FlatList,
  ScrollView,
  Platform,
} from "react-native";
import { FontAwesome6 } from "@expo/vector-icons";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { SeaStatusType } from "@src/types/Common";
import { fontFamily } from "@src/theme/typography";
import { theme } from "@src/theme";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";
import { SeaStack } from "../SeaStack/SeaStack";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { colors } from "@src/theme/colors";
import { useDeviceWidth } from "@src/hooks/useDevice";
import { SeaLoadingSpinner } from "@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner";
import { SeaPopover } from "../SeaPopover/SeaPopover";

const OPEN_GROUP_ACTION = "OPEN_GROUP";
const CLOSE_GROUP_ACTION = "CLOSE_GROUP";
const EXPAND_ALL_ACTION = "EXPAND_ALL";
const COLLAPSE_ALL_ACTION = "COLLAPSE_ALL";

const ROW_HEIGHT = 60;
const ROW_BOTTOM_MARGIN = 5;

const COMPACT_ROW_HEIGHT = 30;
const COMPACT_ROW_PADDING_VERTICAL = 8;
const COMPACT_ROW_BOTTOM_MARGIN = 4;

export interface CompactModeOptions {
  hideLabel?: boolean;
  hideRow?: boolean;
  isTitle?: boolean;
  isThumbnail?: boolean;
}

export interface SeaTableColumn<T> {
  label: string;
  /** An optional handler that will be executed upon tapping the column header */
  onPress?: () => void;
  compactModeOptions?: CompactModeOptions;
  value?: (data: T) => Element | string;
  icon?: (data: T) => ReactElement<typeof SeaIcon>;
  render?: (data: T) => React.ReactNode;
  /** Width in units. If provided, will override widthPercentage */
  width?: number;
  /* The percent of the available space that the column should occupy. Value must be between 0 and 1 */
  widthPercentage?: number;
  isHidden?: boolean;
  style?: StyleProp<ViewStyle>;
}

export interface SeaTableRow<T> {
  data: T;
  onPress?: (data: T) => void;
  group?: (data: T) => string;
  status?: SeaStatusType;
  style?: StyleProp<ViewStyle>;
}

export interface SeaTableProps<T> {
  columns: SeaTableColumn<T>[];
  rows: SeaTableRow<T>[];
  showGroupedTable?: boolean;
  sortGroupsByTitle?: (a: string, b: string) => number;
  sortFunction?: (a: T, b: T) => number;
  /** Allow the table to scroll horizontally */
  scrollable?: boolean;
  /* Compact view will be shown if screen width is less than this number */
  compactViewBreakpoint?: number;
  style?: StyleProp<ViewStyle>;
}

function reducer(
  state: Record<string, boolean>,
  action: { type: string; category: string; categories?: string[] },
) {
  if (action.type === EXPAND_ALL_ACTION) {
    if (action.categories && action.categories.length > 0) {
      const newState = { ...state };
      action.categories.forEach((category) => (newState[category] = true));
      return newState;
    } else {
      const newState = { ...state };
      Object.keys(newState).forEach((key) => (newState[key] = true));
      return newState;
    }
  }

  if (action.type === COLLAPSE_ALL_ACTION) {
    const newState = { ...state };
    Object.keys(newState).forEach((key) => (newState[key] = false));

    return newState;
  }

  return {
    ...state,
    [action.category]: action.type === OPEN_GROUP_ACTION,
  };
}

/**
 * TODO: Mobile view for the table. Waiting for the designs
 */
export const SeaTable = <T,>({
  columns,
  rows,
  style,
  showGroupedTable,
  sortGroupsByTitle,
  sortFunction,
  scrollable,
}: SeaTableProps<T>) => {
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [isLayoutReady, setIsLayoutReady] = useState(false);

  const { isMobileWidth } = useDeviceWidth();

  const isCompactMode = useMemo(() => {
    return isMobileWidth;
  }, [isMobileWidth]);

  return (
    <View
      onLayout={(e) => {
        const layoutWidth = e.nativeEvent.layout.width;
        if (layoutWidth > 0 && layoutWidth !== containerWidth) {
          setContainerWidth(layoutWidth);
          setIsLayoutReady(true);
        }
      }}
      style={[
        styles.container,
        style,
        isMobileWidth ? { paddingHorizontal: 5 } : {},
      ]}
    >
      {isLayoutReady ? (
        <>
          {showGroupedTable ? (
            <GroupedTable
              columns={columns}
              rows={rows}
              sortGroupsByTitle={sortGroupsByTitle}
              sortFunction={sortFunction}
              isCompactMode={isCompactMode}
              tableWidth={containerWidth - 80}
            />
          ) : !isCompactMode && scrollable ? (
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View>
                {!isCompactMode && (
                  <Header columns={columns} tableWidth={containerWidth} />
                )}
                <Table
                  columns={columns}
                  rows={rows}
                  isCompactMode={isCompactMode}
                  tableWidth={!isCompactMode ? containerWidth : containerWidth} // Leave space for the scroll indicator
                />
              </View>
            </ScrollView>
          ) : (
            <>
              {!isCompactMode && (
                <Header columns={columns} tableWidth={containerWidth} />
              )}
              <Table
                columns={columns}
                rows={rows}
                isCompactMode={isCompactMode}
                tableWidth={containerWidth}
              />
            </>
          )}
        </>
      ) : (
        <SeaLoadingSpinner />
      )}
    </View>
  );
};

interface TableProps<T> {
  rows: SeaTableRow<T>[];
  columns: SeaTableColumn<T>[];
  isCompactMode: boolean;
  tableWidth: number;
}

const Table = <T,>({
  rows,
  columns,
  isCompactMode,
  tableWidth,
}: TableProps<T>) => {
  const renderItem = useCallback(
    ({ item: row, index }: { item: SeaTableRow<T>; index: number }) => {
      const rowStyleOverrides: ViewStyle = {
        borderWidth: 1,
        borderColor: theme.colors.borderColor,
        backgroundColor: theme.colors.white,
      };

      const errorStyleOverrides: ViewStyle = {
        borderLeftColor: getColorForStatus(SeaStatusType.Error),
        borderLeftWidth: 8,
      };

      const sharedStyle = [
        row.style,
        rowStyleOverrides,
        row.status === SeaStatusType.Error
          ? errorStyleOverrides
          : { paddingLeft: 18 }, // This padding ensures the table doesn't get out of alignment when rows have the 'error tab'
      ];

      return isCompactMode ? (
        <MemoizedCompactRow
          key={index}
          row={row}
          columns={columns}
          tableWidth={tableWidth}
          style={sharedStyle}
        />
      ) : (
        <MemoizedRow
          key={index}
          row={row}
          columns={columns}
          tableWidth={tableWidth}
          style={sharedStyle}
        />
      );
    },
    [columns, isCompactMode, tableWidth], // 👈 dependencies that impact this function
  );

  // Use memo to prevent unnecessary re-renders
  const keyExtractor = useMemo(
    () => (item: SeaTableRow<T>, index: number) => `row-${index}`,
    [],
  );

  return (
    <FlatList
      data={rows}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      initialNumToRender={10}
      maxToRenderPerBatch={5}
      windowSize={10}
      // TODO: Ideally pass the layout here
      // getItemLayout={getItemLayout}
      removeClippedSubviews={true}
      updateCellsBatchingPeriod={50}
    />
  );
};

const GroupedSection = <T,>({
  isOpen,
  dispatch,
  category,
  groupRows,
  columns,
  isCompactMode,
  isFirstGroup,
  tableWidth,
}: {
  category: string;
  groupRows: SeaTableRow<T>[];
  isOpen: boolean;
  dispatch: Dispatch<{ type: string; category: string }>;
  columns: SeaTableColumn<T>[];
  isCompactMode: boolean;
  isFirstGroup: boolean;
  tableWidth: number;
}) => {
  const height = useSharedValue(isOpen ? 1 : 0);
  height.value = withTiming(isOpen ? 1 : 0, { duration: 250 });

  // Use different approach for mobile platforms in compact mode
  const isMobileNative = Platform.OS !== "web" && isCompactMode;

  const animatedStyle = useAnimatedStyle(() => ({
    height: isMobileNative
      ? undefined // Don't set height on mobile native
      : height.value *
        groupRows.length *
        (isCompactMode
          ? columns.length * (COMPACT_ROW_HEIGHT + COMPACT_ROW_BOTTOM_MARGIN) +
            COMPACT_ROW_PADDING_VERTICAL * 2 +
            ROW_BOTTOM_MARGIN
          : ROW_HEIGHT + ROW_BOTTOM_MARGIN),
    overflow: isMobileNative ? "visible" : "hidden",
    display: isMobileNative && !isOpen ? "none" : "flex",
  }));

  return (
    <>
      <TouchableOpacity
        onPress={() =>
          dispatch({
            type: isOpen ? CLOSE_GROUP_ACTION : OPEN_GROUP_ACTION,
            category,
          })
        }
      >
        <View
          style={[
            styles.headerRow,
            {
              marginTop: isFirstGroup ? 0 : 20,
            },
          ]}
        >
          <Text style={styles.headerRowTitle}>
            {category} {""}
            <Text
              style={{
                fontWeight: "300",
              }}
            >
              | {groupRows.length}
            </Text>
          </Text>
          <FontAwesome6
            size={14}
            name={isOpen ? "chevron-up" : "chevron-down"}
            color={theme.colors.text.primary}
          />
        </View>
      </TouchableOpacity>

      {isMobileNative ? (
        // For mobile native in compact mode, use simple conditional rendering
        isOpen && (
          <View>
            <Table
              rows={groupRows}
              columns={columns}
              isCompactMode={isCompactMode}
              tableWidth={tableWidth}
            />
          </View>
        )
      ) : (
        // For web or non-compact mode, use the animation
        <Animated.View style={animatedStyle}>
          <Table
            rows={groupRows}
            columns={columns}
            isCompactMode={isCompactMode}
            tableWidth={tableWidth}
          />
        </Animated.View>
      )}
    </>
  );
};

const GroupedTable = <T,>({
  columns,
  rows,
  sortFunction,
  sortGroupsByTitle,
  isCompactMode,
  tableWidth,
}: {
  columns: SeaTableColumn<T>[];
  rows: SeaTableRow<T>[];
  sortFunction?: (a: T, b: T) => number;
  sortGroupsByTitle?: (a: string, b: string) => number;
  isCompactMode: boolean;
  tableWidth: number;
}) => {
  const { categories, groups } = useMemo(() => {
    const groupMap: Record<string, SeaTableRow<T>[]> = {};
    for (const row of rows) {
      const group = row.group ? row.group(row.data) : "";
      if (!groupMap[group]) groupMap[group] = [];
      groupMap[group].push(row);
    }

    const sortedCategories = Object.keys(groupMap).sort(
      sortGroupsByTitle ?? (() => 0),
    );

    for (const key of sortedCategories) {
      if (sortFunction) {
        groupMap[key].sort((a, b) => sortFunction(a.data, b.data));
      }
    }

    return { categories: sortedCategories, groups: groupMap };
  }, [rows, sortFunction, sortGroupsByTitle]);

  // Use memo to prevent unnecessary re-renders
  const keyExtractor = useMemo(() => (item: string) => `group-${item}`, []);

  const [state, dispatch] = useReducer(reducer, {});

  useEffect(() => {
    dispatch({ type: EXPAND_ALL_ACTION, category: "", categories });
  }, [categories]);

  return (
    <>
      {!isCompactMode && (
        <Header
          columns={columns}
          tableWidth={tableWidth}
          onCollapseAll={() =>
            dispatch({ type: COLLAPSE_ALL_ACTION, category: "" })
          }
          onExpandAll={() =>
            dispatch({ type: EXPAND_ALL_ACTION, category: "" })
          }
        />
      )}
      <FlatList
        data={categories}
        keyExtractor={keyExtractor}
        maxToRenderPerBatch={3}
        windowSize={5}
        renderItem={({ item: category, index }) => (
          <GroupedSection
            key={category}
            category={category}
            isOpen={state[category]}
            dispatch={dispatch}
            groupRows={groups[category]}
            columns={columns}
            isCompactMode={isCompactMode}
            isFirstGroup={index === 0}
            tableWidth={tableWidth}
          />
        )}
      />
    </>
  );
};

interface HeaderProps<T> {
  columns: SeaTableColumn<T>[];
  tableWidth: number;
  onCollapseAll?: () => void;
  onExpandAll?: () => void;
}

const Header = <T,>({
  columns,
  tableWidth,
  onCollapseAll,
  onExpandAll,
}: HeaderProps<T>) => (
  <View style={styles.header}>
    <View style={styles.columns}>
      {columns
        .filter((col) => !col.isHidden)
        .map((col) => (
          <Pressable
            key={col.label}
            onPress={() => col.onPress && col.onPress()}
            style={[
              styles.headerColumn,
              {
                width: getCellPixelWidth(
                  columns,
                  // Remove the width of the expand & collapse buttons
                  tableWidth,
                  col,
                ),
              },
              {
                paddingHorizontal: 4,
              },
            ]}
          >
            <Text
              numberOfLines={1}
              ellipsizeMode={"tail"}
              style={styles.headerText}
            >
              {col.label}
            </Text>
          </Pressable>
        ))}

      {onExpandAll && onCollapseAll && (
        <SeaStack style={styles.buttons} direction={"row"}>
          <Pressable onPress={onExpandAll}>
            <SeaIcon icon={"unfold_more"} color={colors.grey} />
          </Pressable>
          <Pressable onPress={onCollapseAll}>
            <SeaIcon icon={"unfold_less"} color={colors.grey} />
          </Pressable>
        </SeaStack>
      )}
    </View>
  </View>
);

export interface RowProps<T> {
  row: SeaTableRow<T>;
  columns: SeaTableColumn<T>[];
  style?: StyleProp<ViewStyle>;
  tableWidth: number;
}

const Row = <T,>({ row, columns, style, tableWidth }: RowProps<T>) => {
  return (
    <TouchableOpacity
      disabled={!row.onPress}
      onPress={() => row.onPress?.(row.data)}
    >
      <View style={[styles.row, style]}>
        {columns
          .filter((col) => !col.isHidden)
          .map((col, i) => (
            <View
              key={i}
              style={[
                styles.cell,
                {
                  width: getCellPixelWidth(columns, tableWidth, col),
                },
              ]}
            >
              <View
                style={{
                  flex: 1,
                  width: "100%",
                }}
              >
                {col.render ? (
                  <View style={{ flex: 1, justifyContent: "center" }}>
                    {col.render(row.data)}
                  </View>
                ) : (
                  <SeaStack
                    direction={"row"}
                    gap={6}
                    style={{ flex: 1, width: "100%" }}
                  >
                    {col.icon?.(row.data)}
                    <SeaTypography
                      variant={"value"}
                      textStyle={styles.cellText}
                      numberOfLines={2}
                      ellipsizeMode={"tail"}
                      containerStyle={{ flex: 1, width: "100%" }}
                    >
                      {col.value?.(row.data)}
                    </SeaTypography>
                  </SeaStack>
                )}
              </View>
            </View>
          ))}
      </View>
    </TouchableOpacity>
  );
};
const MemoizedRow = React.memo(Row);

const CompactRow = <T,>({ row, columns, style }: RowProps<T>) => {
  const thumbnail = columns
    .find((col) => col.compactModeOptions?.isThumbnail)
    ?.render?.(row.data);

  return (
    <TouchableOpacity
      disabled={!row.onPress}
      onPress={() => row.onPress?.(row.data)}
    >
      <SeaStack direction={"row"} style={[styles.compactRow, style]}>
        {thumbnail && (
          <View style={{ width: 70, marginRight: 8 }}>{thumbnail}</View>
        )}
        <View style={{ flex: 1 }}>
          {columns
            .filter(
              (col) =>
                !col.isHidden &&
                !col.compactModeOptions?.hideRow &&
                !col.compactModeOptions?.isThumbnail,
            )
            .map((col, i) => (
              <View style={styles.compactRowRow} key={i}>
                {!col.compactModeOptions?.hideLabel && (
                  <SeaTypography
                    variant={"label"}
                    containerStyle={styles.compactRowLabel}
                    fontWeight={"bold"}
                  >
                    {col.label}
                  </SeaTypography>
                )}
                {col.render ? (
                  col.render(row.data)
                ) : (
                  <SeaStack
                    direction={"row"}
                    gap={6}
                    style={styles.compactRowValue}
                  >
                    {col.icon?.(row.data)}
                    <SeaTypography
                      variant={
                        col.compactModeOptions?.isTitle ? "subtitle" : "value"
                      }
                      containerStyle={{ flex: 1, width: "100%" }}
                      numberOfLines={2}
                      ellipsizeMode={"tail"}
                    >
                      {col.value?.(row.data)}
                    </SeaTypography>
                  </SeaStack>
                )}
              </View>
            ))}
        </View>
      </SeaStack>
    </TouchableOpacity>
  );
};
const MemoizedCompactRow = React.memo(CompactRow);

const getCellPixelWidth = <T,>(
  columns: SeaTableColumn<T>[],
  tableWidth: number,
  col: SeaTableColumn<T>,
): number => {
  const visibleColumns = columns.filter((c) => !c.isHidden);

  // Total fixed width from columns with explicit pixel widths
  const totalFixedWidth = visibleColumns.reduce((sum, c) => {
    return sum + (c.width ?? 0);
  }, 0);

  // Remaining width available for flexible columns
  const remainingWidth = tableWidth - totalFixedWidth;

  // All flexible columns (no fixed pixel width)
  const flexibleColumns = visibleColumns.filter((c) => c.width == null);

  // Sum of widthPercentage values or default to equal distribution
  const totalPercentage = flexibleColumns.reduce((sum, c) => {
    return sum + (c.widthPercentage ?? 1 / flexibleColumns.length);
  }, 0);

  // If the column has fixed width, return that
  if (col?.width != null) return col.width;

  // If flexible, calculate proportional width
  const colPercentage = col.widthPercentage ?? 1 / flexibleColumns.length;
  const effectivePercentage = colPercentage / totalPercentage;

  return remainingWidth * effectivePercentage;
};

const getColorForStatus = (status: SeaStatusType) => {
  switch (status) {
    case SeaStatusType.Ok:
    case SeaStatusType.Attention:
      return theme.colors.white;
    case SeaStatusType.Warning:
      return theme.colors.status.warnPrimary;
    case SeaStatusType.Error:
      return theme.colors.status.error;
    case SeaStatusType.Critical:
      return theme.colors.status.critical;
  }
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  header: {
    flexDirection: "row",
    paddingHorizontal: 20,
    paddingLeft: 25,
    justifyContent: "space-between",
  },
  columns: {
    flexDirection: "row",
    // height: 36,
    marginBottom: 4,
  },
  buttons: {},
  headerColumn: {
    paddingVertical: 4,
    justifyContent: "center",
    alignItems: "flex-start",
    flexWrap: "wrap",
    flexDirection: "row",
    paddingHorizontal: 5,
  },
  headerText: {
    textTransform: "uppercase",
    fontFamily: fontFamily.BODY_FONT,
    fontSize: 12,
    fontWeight: "600",
    color: theme.colors.text.body,
    flex: 1,
    flexShrink: 1,
  },
  headerRow: {
    borderRadius: 8,
    backgroundColor: theme.colors.lightGrey,
    marginBottom: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  headerRowTitle: {
    fontSize: 12,
    color: theme.colors.text.primary,
    fontWeight: "bold",
    fontFamily: fontFamily.BODY_FONT,
    letterSpacing: 0.5,
    textTransform: "uppercase",
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    minHeight: ROW_HEIGHT,
    marginBottom: ROW_BOTTOM_MARGIN,
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  cell: {
    justifyContent: "center",
    alignItems: "flex-start",
    paddingHorizontal: 10,
  },
  cellText: {
    textAlign: "left",
  },
  compactRow: {
    marginBottom: ROW_BOTTOM_MARGIN,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
  },
  compactRowRow: {
    marginBottom: COMPACT_ROW_BOTTOM_MARGIN,
    paddingHorizontal: 4,
    flexDirection: "row",
    alignItems: "center",
  },
  compactRowLabel: {
    flex: 1,
  },
  compactRowValue: {
    flex: 2,
  },
});
