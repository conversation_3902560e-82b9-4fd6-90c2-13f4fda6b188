import React, { useMemo } from "react";
import { Equipment } from "@src/shared-state/VesselMaintenance/equipment";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import { useLicenseeSettings } from "@src/hooks/useLicenseeSettings";
import { MaintenanceTaskCompleted } from "@src/shared-state/VesselMaintenance/maintenanceTasksCompleted";
import {
  formatDateShort,
  formatShortTimeDurationHrsMinsView,
} from "@src/lib/datesAndTime";
import { formatValue, renderCamelCase, truncateText } from "@src/lib/util";
import { renderFullNameForUserId } from "@src/shared-state/Core/users";
import { sharedState } from "@src/shared-state/shared-state";
import { SeaTableImage } from "@src/components/_atoms/SeaTable/SeaTableImage";

interface EquipmentHistoryTableProps {
  selectedEquipment: Equipment;
}

export function EquipmentHistoryTable({
  selectedEquipment,
}: EquipmentHistoryTableProps) {
  const { hasTimeTrackingEnabled } = useLicenseeSettings();

  const maintenanceTasksCompleted =
    sharedState.maintenanceTasksCompleted.use(!!selectedEquipment);

  const associatedTasksCompleted = useMemo(() => {
    if (!selectedEquipment || !maintenanceTasksCompleted?.all) return [];

    return maintenanceTasksCompleted.all
      .filter(
        (taskCompleted) => taskCompleted.equipmentId === selectedEquipment.id,
      )
      .sort((a, b) => b.whenCompleted - a.whenCompleted);
  }, [selectedEquipment, maintenanceTasksCompleted]);

  return (
    <SeaTable
      columns={buildColumn(hasTimeTrackingEnabled)}
      rows={buildRow(associatedTasksCompleted, () => alert("TODO View Task"))}
      style={{
        marginBottom: 20,
      }}
    />
  );
}

const buildRow = (
  items: MaintenanceTaskCompleted[],
  onPress: (item: MaintenanceTaskCompleted) => void,
) => {
  return items.map((item) => ({
    data: item,
    onPress: (item: MaintenanceTaskCompleted) => onPress(item),
  }));
};

const buildColumn = (hasTimeTrackingEnabled: boolean) => {
  return [
    {
      label: "Date",
      value: (item) => formatDateShort(item.whenCompleted),
    },
    {
      label: "Task",
      value: (item) => truncateText(formatValue(item.task)),
    },
    {
      label: "Notes",
      value: (item) => truncateText(formatValue(item.notes), 40),
    },
    {
      label: "Type",
      value: (item) =>
        formatValue(
          item.type === "unscheduled" ? "Job" : renderCamelCase(item.type),
        ),
    },
    {
      label: "Completed By",
      value: (item) => renderFullNameForUserId(item.completedBy),
    },
    ...(hasTimeTrackingEnabled
      ? [
          {
            label: "Actual Time",
            value: (item: MaintenanceTaskCompleted) =>
              formatValue(
                item.actualTime
                  ? formatShortTimeDurationHrsMinsView(item.actualTime)
                  : "-",
              ),
          },
        ]
      : []),
    {
      label: "",
      render: (item) => <SeaTableImage files={item.files} />,
    },
  ] as SeaTableColumn<MaintenanceTaskCompleted>[];
};
