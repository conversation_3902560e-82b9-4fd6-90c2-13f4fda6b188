import React, { useMemo } from "react";
import { Equipment } from "@src/shared-state/VesselMaintenance/equipment";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import { SparePart } from "@src/shared-state/VesselMaintenance/spareParts";
import { formatValue } from "@src/lib/util";
import { CategoriesData, renderCategoryName } from "@src/lib/categories";
import { sharedState } from "@src/shared-state/shared-state";
import { SeaTableImage } from "@src/components/_atoms/SeaTable/SeaTableImage";

interface SparePartsTableProps {
  selectedEquipment: Equipment;
}

export function SparePartsTable({ selectedEquipment }: SparePartsTableProps) {
  const vesselLocations = sharedState.vesselLocations.use(!!selectedEquipment);
  const spareParts = sharedState.spareParts.use(!!selectedEquipment);

  const equipmentSpareParts = useMemo(() => {
    if (!selectedEquipment || !spareParts?.all) return [];

    return spareParts.all.filter((sparePart) =>
      sparePart.equipmentIds?.some((id) => id === selectedEquipment.id),
    );
  }, [selectedEquipment, spareParts]);

  return (
    <SeaTable
      columns={buildColumn(vesselLocations)}
      rows={buildRow(equipmentSpareParts, () => alert("TODO View Task"))}
      style={{
        marginBottom: 20,
      }}
    />
  );
}

const buildRow = (items: SparePart[], onPress: (item: SparePart) => void) => {
  return items.map((item) => ({
    data: item,
    onPress: (item: SparePart) => onPress(item),
  }));
};

const buildColumn = (vesselLocations?: CategoriesData) => {
  return [
    {
      label: "",
      render: (item) => <SeaTableImage files={item.files} />,
    },
    {
      label: "Item",
      value: (item) => formatValue(item.item),
    },
    {
      label: "Quantity",
      value: (item) => formatValue(item.quantity),
    },
    {
      label: "Location",
      value: (item) =>
        formatValue(renderCategoryName(item.locationId, vesselLocations)),
    },
    {
      label: "Part #",
      value: (item) => formatValue(item.partNum),
    },
  ] as SeaTableColumn<SparePart>[];
};
