import React, { useMemo, useState } from "react";
import { StyleSheet } from "react-native";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { sharedState } from "@src/shared-state/shared-state";
import { ScheduledMaintenanceTask } from "@src/shared-state/VesselMaintenance/maintenanceSchedule";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import { formatSparePartsList, formatValue, truncateText } from "@src/lib/util";
import {
  formatDateShort,
  formatShortTimeDurationHrsMinsView,
} from "@src/lib/datesAndTime";
import { MaintenanceTaskCompleted } from "@src/shared-state/VesselMaintenance/maintenanceTasksCompleted";
import { renderFullNameForUserId } from "@src/shared-state/Core/users";
import { CompleteMaintenanceScheduleDrawer } from "./CompleteMaintenanceScheduleDrawer";
import { SeaTableImage } from "@src/components/_atoms/SeaTable/SeaTableImage";

const seaTableColumns = [
  {
    label: "Date",
    value: (item) => formatDateShort(item.whenCompleted),
    widthPercentage: 0.1,
  },
  {
    label: "By",
    value: (item) => truncateText(renderFullNameForUserId(item.completedBy)),
    widthPercentage: 0.1,
  },
  {
    label: "Notes",
    value: (item) => truncateText(item.notes ?? "", 50),
    widthPercentage: 0.4,
  },
  {
    label: "",
    render: (item) => <SeaTableImage files={item.files} />,
    widthPercentage: 0.2,
  },
] as SeaTableColumn<MaintenanceTaskCompleted>[];

interface TaskHistoryTableProps {
  selectedSchedule: ScheduledMaintenanceTask;
}

export const TaskHistoryTable = ({
  selectedSchedule,
}: TaskHistoryTableProps) => {
  const maintenanceTasksCompleted = sharedState.maintenanceTasksCompleted.use();
  const licenseeSettings = sharedState.licenseeSettings.use();
  const spareParts = sharedState.spareParts.use();

  const [openHistoryModal, setopenHistoryModal] = useState(false);

  const hasTimeTrackingEnabled = useMemo(() => {
    return licenseeSettings?.hasMaintenanceTaskTime ?? false;
  }, [licenseeSettings]);

  const equipmentSpareParts = useMemo(() => {
    if (!selectedSchedule?.equipmentId || !spareParts?.all) return undefined;

    return spareParts.all.filter((sparePart) =>
      sparePart.equipmentIds?.includes(selectedSchedule.equipmentId),
    );
  }, [selectedSchedule, spareParts]);

  /**
   * This creates an array of the columns those are conditionally added at specific position or
   * have dependencies from the shared state.
   */
  const updatedColumns = useMemo(() => {
    let newColumns = [...seaTableColumns];

    const byIndex = newColumns.findIndex((col) => col.label === "By");
    const engineHourIndex = newColumns.findIndex(
      (col) => col.label === "Engine Hours",
    );
    const partsUsedIndex = newColumns.findIndex(
      (col) => col.label === "Parts Used",
    );
    const actualTimeIndex = newColumns.findIndex(
      (col) => col.label === "Actual time",
    );

    // Add "Actual time" after "Parts Used" if it does not exist
    if (
      hasTimeTrackingEnabled &&
      partsUsedIndex !== -1 &&
      actualTimeIndex === -1
    ) {
      newColumns = [
        ...newColumns.slice(0, partsUsedIndex + 1),
        {
          label: "Actual time",
          value: (item) =>
            formatValue(
              item.actualTime
                ? formatShortTimeDurationHrsMinsView(item.actualTime)
                : "-",
            ),
        },
        ...newColumns.slice(partsUsedIndex + 1),
      ];
    }

    // Add "Engine Hours" before "By" column (if not already present)
    if (selectedSchedule.engineId && byIndex !== -1 && engineHourIndex === -1) {
      newColumns = [
        ...newColumns.slice(0, byIndex),
        {
          label: "Engine Hours",
          value: (item) =>
            item?.engineHours ? formatValue(item.engineHours) : "-",
          widthPercentage: 0.1,
        },
        ...newColumns.slice(byIndex),
      ];
    }

    // Ensure "By" column is before "Parts Used"
    const newByIndex = newColumns.findIndex((col) => col.label === "By");
    const newPartsUsedIndex = newColumns.findIndex(
      (col) => col.label === "Parts Used",
    );

    if (newByIndex !== -1 && newPartsUsedIndex === -1) {
      newColumns = [
        ...newColumns.slice(0, newByIndex + 1),
        {
          label: "Parts Used",
          value: (item) =>
            item.spareParts
              ? formatValue(
                  formatSparePartsList(item.spareParts, equipmentSpareParts),
                )
              : "-",
        },
        ...newColumns.slice(newByIndex + 1),
      ];
    }

    return newColumns;
  }, [
    seaTableColumns,
    hasTimeTrackingEnabled,
    selectedSchedule,
    equipmentSpareParts,
  ]);

  const sortedMaintenanceTasksCompleted = useMemo(() => {
    if (
      selectedSchedule?.id &&
      maintenanceTasksCompleted?.byMaintenanceTaskId[selectedSchedule.id]
    ) {
      const tasksSorted = [
        ...maintenanceTasksCompleted.byMaintenanceTaskId[selectedSchedule.id],
      ].sort((a, b) => b.whenCompleted - a.whenCompleted);

      // Update whenLastService if the highest whenCompleted changes, or in case the wrong value has been set previously
      if (tasksSorted.length > 0) {
        const highestWhenCompleted = tasksSorted[0].whenCompleted;
        if (selectedSchedule.whenLastService !== highestWhenCompleted) {
          selectedSchedule.whenLastService = highestWhenCompleted;
        }
      }

      return tasksSorted;
    }

    return [];
  }, [selectedSchedule, maintenanceTasksCompleted?.byMaintenanceTaskId]);

  return (
    <RequirePermissions
      role={"maintenanceHistory"}
      level={permissionLevels.VIEW}
    >
      <SeaTable
        columns={updatedColumns}
        rows={buildRows(sortedMaintenanceTasksCompleted, (item) =>
          setopenHistoryModal(true),
        )}
      />

      {openHistoryModal && (
        <CompleteMaintenanceScheduleDrawer
          visible={openHistoryModal}
          onClose={() => setopenHistoryModal(false)}
          selectedScheduleId={selectedSchedule.id}
          editHistory
        />
      )}
    </RequirePermissions>
  );
};

const buildRows = (
  items: MaintenanceTaskCompleted[],
  onPress: (item: MaintenanceTaskCompleted) => void,
) => {
  return items.map((item) => ({
    data: item,
    onPress: (item: MaintenanceTaskCompleted) => onPress(item),
  }));
};

const styles = StyleSheet.create({});
