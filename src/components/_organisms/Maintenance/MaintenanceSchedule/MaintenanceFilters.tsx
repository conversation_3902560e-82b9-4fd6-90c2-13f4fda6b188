import React from "react";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { View } from "react-native";
import { SeaDropdown } from "@src/components/_atoms/SeaDropdown/SeaDropdown";
import { SeaFilterSearch } from "@src/components/_atoms/SeaFilterSearch/SeaFilterSearch";

type Options = {
  label: string;
  value: string;
};

type FilterProps<T> = {
  value: string;
  setValue: (value: string) => void;
  options: Options[];
};

interface MaintenanceFiltersProps {
  setSeaFilterTagsValue?: () => void;
  searchFilter?: {
    searchValue: string;
    setSearchValue: (value: string) => void;
  };
  systemFilter?: FilterProps<string>;
  equipmentFilter?: FilterProps<string>;
  assignedToFilter?: FilterProps<string>;
  locationFilter?: FilterProps<string>;
  completedByFilter?: FilterProps<string>;
}

const DropdownFilter = ({
  prefixLabel,
  filter,
  onClear,
}: {
  prefixLabel?: string;
  filter?: FilterProps<string>;
  onClear?: () => void;
}) => {
  if (!filter) return null;

  const { value, setValue, options } = filter;

  return (
    <SeaDropdown
      items={options}
      value={value}
      prefixLabel={prefixLabel}
      onSelect={(selectedValue) => {
        setValue(selectedValue);
        onClear && onClear();
      }}
      rounded
      noValidation
    />
  );
};

export const MaintenanceFilters = ({
  searchFilter,
  systemFilter,
  equipmentFilter,
  setSeaFilterTagsValue,
  assignedToFilter,
  locationFilter,
  completedByFilter,
}: MaintenanceFiltersProps) => {
  return (
    <SeaStack direction="row" justify="between" align="center" gap={5}>
      <DropdownFilter filter={systemFilter} prefixLabel="System" />
      <DropdownFilter filter={equipmentFilter} prefixLabel="Equipment" />
      <DropdownFilter filter={assignedToFilter} prefixLabel="Assigned To" />
      <DropdownFilter filter={locationFilter} prefixLabel="Location" />
      <DropdownFilter filter={completedByFilter} prefixLabel="Completed by" />
      {searchFilter && (
        <SeaFilterSearch
          value={searchFilter.searchValue}
          onChangeText={searchFilter.setSearchValue}
          noValidation
        />
      )}

      {/* <SeaButton
        onPress={() => {
          systemFilter && systemFilter.setValue("");
          equipmentFilter && equipmentFilter.setValue("");
          searchFilter && searchFilter.setSearchValue("");
          assignedToFilter && assignedToFilter.setValue("");
          locationFilter && locationFilter.setValue("");
          completedByFilter && completedByFilter.setValue("");
          setSeaFilterTagsValue && setSeaFilterTagsValue();
        }}
        variant={SeaButtonVariant.Primary}
        label="Reset"
      /> */}
    </SeaStack>
  );
};
