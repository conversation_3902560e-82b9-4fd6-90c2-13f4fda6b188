import React, { useMemo } from "react";
import { ScheduledMaintenanceTask } from "@src/shared-state/VesselMaintenance/maintenanceSchedule";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { sharedState } from "@src/shared-state/shared-state";
import { MaintenanceTaskCompleted } from "@src/shared-state/VesselMaintenance/maintenanceTasksCompleted";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import {
  formatDateShort,
  formatShortTimeDurationHrsMinsView,
} from "@src/lib/datesAndTime";
import { formatValue, renderCamelCase, truncateText } from "@src/lib/util";
import SeaFileImage from "@src/components/_atoms/SeaFileImage/SeaFileImage";
import { renderFullNameForUserId } from "@src/shared-state/Core/users";
import { SeaTableImage } from "@src/components/_atoms/SeaTable/SeaTableImage";

const seaTableColumns = [
  {
    label: "Date",
    value: (item) => formatDateShort(item.whenCompleted),
    widthPercentage: 0.1,
  },
  {
    label: "Task",
    value: (item) => truncateText(formatValue(item.task), 50),
    widthPercentage: 0.2,
  },
  {
    label: "Notes",
    value: (item) => truncateText(item.notes ?? "", 50),
    widthPercentage: 0.3,
  },
  {
    label: "Type",
    value: (item) =>
      formatValue(
        item.type === "unscheduled" ? "Job" : renderCamelCase(item.type),
      ),
    widthPercentage: 0.1,
  },
  {
    label: "Completed By",
    value: (item) =>
      truncateText(renderFullNameForUserId(item.completedBy) || "", 50),
    widthPercentage: 0.2,
  },
  {
    label: "",
    render: (item) => <SeaTableImage files={item.files} />,
    widthPercentage: 0.1,
  },
] as SeaTableColumn<MaintenanceTaskCompleted>[];

interface EquipmentHistoryTableProps {
  selectedSchedule: ScheduledMaintenanceTask;
}

export const EquipmentHistoryTable = ({
  selectedSchedule,
}: EquipmentHistoryTableProps) => {
  const maintenanceTasksCompleted = sharedState.maintenanceTasksCompleted.use();
  const licenseeSettings = sharedState.licenseeSettings.use();

  const equipmentTasksCompleted = useMemo(() => {
    if (selectedSchedule && maintenanceTasksCompleted) {
      const tasksCompleted: MaintenanceTaskCompleted[] = [];
      maintenanceTasksCompleted.all.forEach((taskCompleted) => {
        if (taskCompleted.equipmentId === selectedSchedule.equipmentId) {
          tasksCompleted.push(taskCompleted);
        }
      });
      tasksCompleted.sort((a, b) => {
        return b.whenCompleted - a.whenCompleted;
      });
      return tasksCompleted;
    }
    return [];
  }, [selectedSchedule, maintenanceTasksCompleted]);

  const hasTimeTrackingEnabled = useMemo(() => {
    return licenseeSettings?.hasMaintenanceTaskTime ?? false;
  }, [licenseeSettings]);

  /**
   * This creates an array of the columns those are conditionally added at specific position or
   * have dependencies from the shared state.
   */
  const updatedColumns = useMemo(() => {
    let newColumns = [...seaTableColumns];

    const partsUsedIndex = newColumns.findIndex(
      (col) => col.label === "Completed By",
    );
    const actualTimeIndex = newColumns.findIndex(
      (col) => col.label === "Actual time",
    );

    // Add "Actual time" after "Parts Used" if it does not exist
    if (
      hasTimeTrackingEnabled &&
      partsUsedIndex !== -1 &&
      actualTimeIndex === -1
    ) {
      newColumns = [
        ...newColumns.slice(0, partsUsedIndex + 1),
        {
          label: "Actual time",
          value: (item) =>
            formatValue(
              item.actualTime
                ? formatShortTimeDurationHrsMinsView(item.actualTime)
                : "-",
            ),
        },
        ...newColumns.slice(partsUsedIndex + 1),
      ];
    }

    return newColumns;
  }, [seaTableColumns, hasTimeTrackingEnabled, selectedSchedule]);

  return (
    <RequirePermissions
      role={"maintenanceHistory"}
      level={permissionLevels.VIEW}
    >
      <SeaTable
        columns={updatedColumns}
        rows={buildRows(equipmentTasksCompleted, (item) => alert("TODO"))}
      />
    </RequirePermissions>
  );
};

const buildRows = (
  items: MaintenanceTaskCompleted[],
  onPress: (item: MaintenanceTaskCompleted) => void,
) => {
  return items.map((item) => ({
    data: item,
    onPress: (item: MaintenanceTaskCompleted) => onPress(item),
  }));
};
