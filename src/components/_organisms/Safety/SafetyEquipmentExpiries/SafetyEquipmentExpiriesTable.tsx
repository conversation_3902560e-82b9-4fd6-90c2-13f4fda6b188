import React, { useCallback, useMemo } from "react";
import {
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from "@src/components/_atoms/SeaTable/SeaTable";
import { SafetyEquipmentItem } from "@src/shared-state/VesselSafety/safetyEquipmentItems";
import { View } from "react-native";
import { CategoriesData, renderCategoryName } from "@src/lib/categories";
import { formatValue } from "@src/lib/util";
import {
  formatDateShort,
  getDateDifferenceInDays,
  warnDays,
} from "@src/lib/datesAndTime";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";
import { colors } from "@src/theme/colors";
import { WhenDueStatus } from "@src/components/_molecules/WhenDueStatus/WhenDueStatus";
import { SeaStatusType } from "@src/types/Common";
import { sharedState } from "@src/shared-state/shared-state";
import { DateTime } from "luxon";
import { SeaTableIcon } from "@src/components/_atoms/SeaTable/SeaTableIcon";
import SeaFileImage from "@src/components/_atoms/SeaFileImage/SeaFileImage";
import { useDeviceWidth } from "@src/hooks/useDevice";
import { SeaTableImage } from "@src/components/_atoms/SeaTable/SeaTableImage";

interface SafetyEquipmentExpiriesTableProps {
  items: SafetyEquipmentItem[];
  vesselSafetyItems: CategoriesData;
  vesselLocations: CategoriesData;
  onSelect: (selectedItem: SafetyEquipmentItem) => void;
  showGrouped?: boolean;
}
export const SafetyEquipmentExpiriesTable = ({
  items,
  vesselSafetyItems,
  vesselLocations,
  onSelect,
  showGrouped,
}: SafetyEquipmentExpiriesTableProps) => {
  const { isTabletWidth } = useDeviceWidth();

  const checkIsCritical = useCallback(
    (id: string) => !!vesselSafetyItems?.byId[id]?.isCritical,
    [vesselSafetyItems],
  );

  const columns = useMemo(
    () =>
      buildColumns(
        vesselSafetyItems,
        vesselLocations,
        checkIsCritical,
        isTabletWidth,
      ),
    [vesselSafetyItems, vesselLocations, checkIsCritical, isTabletWidth],
  );

  const rows = useMemo(() => buildRows(items, onSelect), [items]);

  return (
    <View style={{ marginTop: 16 }}>
      <SeaTable
        columns={columns}
        rows={rows}
        showGroupedTable={showGrouped}
        sortFunction={(a, b) => {
          return renderCategoryName(b.itemId, vesselSafetyItems).localeCompare(
            renderCategoryName(a.itemId, vesselSafetyItems),
          );
        }}
      />
    </View>
  );
};

const buildColumns = (
  vesselSafetyItems: CategoriesData,
  vesselLocations: CategoriesData,
  checkIsCritical: (id: string) => boolean,
  isTabletWidth: boolean,
): SeaTableColumn<SafetyEquipmentItem>[] => {
  return [
    {
      label: "",
      width: 60, // Hardcode as it is thumbnail column
      value: (x) => "",
      render: (x) => <SeaTableImage files={x.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },
    {
      label: "Safety Item",
      compactModeOptions: {
        hideLabel: true,
        isTitle: true,
      },
      value: (x) => renderCategoryName(x.itemId, vesselSafetyItems),
    },
    {
      label: "Location",
      isHidden: isTabletWidth,
      compactModeOptions: {
        hideRow: true,
      },
      value: (x) =>
        formatValue(renderCategoryName(x.locationId, vesselLocations)),
      icon: (x) => <SeaTableIcon icon={"location_on"} />,
    },
    {
      label: "Last Service",
      compactModeOptions: {
        hideRow: true,
      },
      value: (x) =>
        x.whenLastChecked ? formatDateShort(x.whenLastChecked) : "",
      icon: (x) =>
        x.whenLastChecked && <SeaTableIcon icon={"calendar_month"} />,
    },
    {
      label: "Service / Expiry",
      value: (x) => (x.dateDue ? formatDateShort(x.dateDue) : ""),
      icon: (x) => x.dateDue && <SeaTableIcon icon={"calendar_month"} />,
    },
    {
      label: "Status",
      compactModeOptions: {
        hideLabel: true,
      },
      render: (x) =>
        x.type !== "nonExpiring" &&
        x.dateDue && (
          <WhenDueStatus
            whenDue={x.dateDue}
            warnDaysThreshold={warnDays.safetyEquipmentExpiries[0]}
            hasFault={x.hasFault ?? false}
          />
        ),
    },
    {
      label: "Critical",
      compactModeOptions: {
        hideRow: true,
      },
      render: (x) => (
        <>
          {checkIsCritical(x.itemId) ? (
            <SeaIcon icon={"flag"} color={colors.grey} />
          ) : null}
        </>
      ),
      widthPercentage: 0.1,
    },
    {
      label: "Reminder",
      isHidden: isTabletWidth,
      compactModeOptions: {
        hideRow: true,
      },
      render: (x) => (
        <>
          {x.emailReminder ? (
            <SeaIcon icon={"mail"} color={colors.grey} />
          ) : null}
        </>
      ),
    },
  ];
};
const buildRows = (
  items: SafetyEquipmentItem[],
  onPress: (item: SafetyEquipmentItem) => void,
): SeaTableRow<SafetyEquipmentItem>[] => {
  return items.map((item) => {
    return {
      data: item,
      status: item.hasFault
        ? SeaStatusType.Critical
        : getRowStatus(item.dateDue),
      onPress: (item) => onPress(item),
      group: (data) => data.type + " equipment",
    };
  });
};

const getRowStatus = (dateDue?: string): SeaStatusType => {
  if (!dateDue) {
    return SeaStatusType.Ok;
  }
  const whenDueDiff = getDateDifferenceInDays(
    DateTime.fromISO(dateDue),
    sharedState.todayMillis.current,
  );

  if (whenDueDiff < 0) return SeaStatusType.Error;
  if (whenDueDiff > 0 && whenDueDiff < warnDays.safetyEquipmentExpiries[0])
    return SeaStatusType.Warning;

  return SeaStatusType.Ok;
};
