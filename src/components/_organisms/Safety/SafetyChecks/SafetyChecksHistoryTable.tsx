import { SafetyCheckCompleted } from "@src/shared-state/VesselSafety/useCompletedSafetyCheckItems";
import React from "react";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import {
  formatDate,
  formatShortTimeDurationHrsMinsView,
} from "@src/lib/datesAndTime";
import { renderFullNameForUserId } from "@src/shared-state/Core/users";
import { formatValue } from "@src/lib/util";
import { SeaTableIcon } from "@src/components/_atoms/SeaTable/SeaTableIcon";
import { SeaTableImage } from "@src/components/_atoms/SeaTable/SeaTableImage";

interface SafetyCheckHistoryTableProps {
  items: SafetyCheckCompleted[];
  hasTimeTrackingEnabled: boolean;
}

export const SafetyCheckHistoryTable: React.FC<
  SafetyCheckHistoryTableProps
> = ({ items, hasTimeTrackingEnabled }) => {
  const columns: SeaTableColumn<SafetyCheckCompleted>[] = [
    {
      label: "Date",
      icon: () => <SeaTableIcon icon={"calendar_month"} />,
      value: (x) => formatDate(x.whenCompleted),
    },
    {
      label: "Completed By",
      icon: () => <SeaTableIcon icon={"person"} />,
      value: (x) => renderFullNameForUserId(x.completedBy),
    },
    {
      label: "Actual Time",
      isHidden: !hasTimeTrackingEnabled,
      icon: (x) => x.actualTime > 0 && <SeaTableIcon icon={"timer"} />,
      value: (x) =>
        x.actualTime > 0
          ? formatValue(formatShortTimeDurationHrsMinsView(x.actualTime, true))
          : "-",
    },
    {
      label: "Notes",
      value: (x) =>
        x.shouldReportFault
          ? "FAULT REPORTED"
          : formatValue(
              x.notes && x.notes?.length > 50
                ? x.notes?.substring(0, 50) + "..."
                : x.notes,
            ),
    },
    {
      label: "",
      render: (x) => <SeaTableImage files={x.files} />,
    },
  ];

  const rows = items.map((item) => ({ data: item }));

  return <SeaTable columns={columns} rows={rows} />;
};
