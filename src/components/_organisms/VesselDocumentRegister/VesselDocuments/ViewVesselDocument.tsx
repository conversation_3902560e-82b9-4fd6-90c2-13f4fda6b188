import { View, Text, Dimensions } from "react-native";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { sharedState } from "@src/shared-state/shared-state";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import { useGlobalSearchParams, useRouter } from "expo-router";
import { useDeviceWidth } from "@src/hooks/useDevice";
import { getFileNameWithExtension, isPdf } from "@src/lib/files";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import { MediaCardFile } from "@src/components/_atoms/SeaMediaCard/SeaMediaCard";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { ScrollView } from "react-native-gesture-handler";
import { SeaPageCard } from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import { SeaButton } from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaEditButton } from "@src/components/_molecules/IconButtons/SeaEditButton";
import { SeaDeleteButton } from "@src/components/_molecules/IconButtons/SeaDeleteButton";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { SeaLabelValue } from "@src/components/_atoms/SeaLabelValue/SeaLabelValue";
import { formatValue } from "@src/lib/util";
import { formatEmailReminder } from "@src/lib/datesAndTime";
import { SeaMedia } from "@src/components/_molecules/SeaMedia/SeaMedia";
import { SeaPDFReader } from "@src/components/_atoms/SeaPDFReader/SeaPDFReader";
import { useItemLinks } from "@src/shared-state/General/useItemLinks";
import SeaRichText from "@src/components/_dom/lexical/SeaRichText/SeaRichText";
import {
  initialRichTextState,
  loadSfdocNew,
  RichTextState,
} from "@src/lib/richText";

const DESKTOP_ITEMS_WIDTH = "100%";

export function ViewVesselDocument() {
  const vessel = sharedState.vessel.use();
  const vesselDocuments = sharedState.vesselDocuments.use();
  const vesselDocumentCategories = sharedState.vesselDocumentCategories.use();

  const { documentId } = useGlobalSearchParams();
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth();
  const { styles } = useStyles(styleSheet);

  const scrollViewRef = useRef<ScrollView>(null);

  const [richTextState, setRichTextState] =
    useState<RichTextState>(initialRichTextState);

  const selectedItem = useMemo(() => {
    return vesselDocuments?.byId[
      Array.isArray(documentId) ? documentId[0] : documentId
    ];
  }, [documentId, vesselDocuments]);

  useEffect(() => {
    if (selectedItem?.sfdoc) {
      const loadDocument = async () => {
        const loadedRichTextState = await loadSfdocNew(selectedItem.sfdoc);

        console.debug("Loaded Rich Text State:", loadedRichTextState);
        setRichTextState(loadedRichTextState as RichTextState);
      };

      loadDocument();
    }
  }, [selectedItem?.sfdoc]);

  //TODO: Implement links for the document
  const links = useItemLinks(selectedItem?.id ?? undefined);

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return [];

    return selectedItem?.files.map((file) => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [
        <SeaDownloadButton
          key={`download-${file}`}
          onPress={() => alert("Coming soon!")}
        />,
      ],
    })) as MediaCardFile[];
  }, [selectedItem]);

  const isPDFCheck = useCallback((file?: string) => {
    if (!file) return false;

    return isPdf(file);
  }, []);

  console.debug("Selected Document:", selectedItem);

  return (
    <RequirePermissions
      role="vesselCertificates"
      level={permissionLevels.VIEW}
      showDenial={true}
    >
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <SeaPageCard
          secondaryActionButton={[
            <SeaEditButton
              key={"Edit"}
              // onPress={() => setIsVisibleEditDrawer(true)}
            />,
            <SeaDeleteButton
              key={"Delete"}
              onPress={() => alert("Coming soon!")}
            />,
          ]}
        >
          <SeaTypography variant="title">{selectedItem?.title}</SeaTypography>
          <SeaStack
            direction={isLargeDesktopWidth ? "row" : "column"}
            gap={20}
            justify="start"
            align="start"
          >
            <SeaStack
              direction="column"
              gap={isDesktopWidth ? 20 : 0}
              align={"start"}
              width={isLargeDesktopWidth ? "60%" : "100%"}
            >
              <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                {selectedItem?.categoryId && vesselDocumentCategories?.byId ? (
                  <SeaLabelValue
                    label={"Category"}
                    value={formatValue(
                      vesselDocumentCategories.byId[selectedItem.categoryId]
                        .name
                    )}
                  />
                ) : null}
              </SeaStack>

              {selectedItem?.type === "renewable" && (
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue
                    label={"Expiry"}
                    value={selectedItem?.dateExpires}
                  />

                  <SeaLabelValue
                    label={"Notification"}
                    value={formatValue(
                      formatEmailReminder(selectedItem?.emailReminder)
                    )}
                  />
                </SeaStack>
              )}
            </SeaStack>
            {uploadedFiles.length > 0 && (
              <View style={{ flex: 1 }}>
                <SeaMedia type="manuals" title="Files" files={uploadedFiles} />
              </View>
            )}
          </SeaStack>
        </SeaPageCard>

        {isPDFCheck(selectedItem?.files?.[0]) && (
          <SeaPDFReader file={selectedItem?.files?.[0]} />
        )}

        {selectedItem?.sfdoc && (
          <View style={styles.richTextContainer}>
            <SeaRichText
              forModal={false}
              visible={true}
              setOnScroll={() => console.log("TT-RT-SetOnScroll")}
              modalContentRef={scrollViewRef}
              richTextState={richTextState}
              onlineStatus={true}
            />
          </View>
        )}
      </ScrollView>

      {/* {isVisibleEditDrawer && (
            <EditVesselCertificateDrawer
              onClose={() => setIsVisibleEditDrawer(false)}
              selectedItem={selectedItem}
              visible={isVisibleEditDrawer}
              type={DrawerMode.Edit}
            />
          )}
    
          {isVisibleRenewDrawer && (
            <RenewVesselCertificateDrawer
              onClose={() => setIsVisibleRenewDrawer(false)}
              selectedItem={selectedItem}
              visible={isVisibleRenewDrawer}
            />
          )} */}
    </RequirePermissions>
  );
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
    height: "100%",
  },
  content: {
    marginBottom: 20,
  },
  contentContainer: {
    flexGrow: 1,
    minHeight: "100%",
  },
  richTextContainer: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
}));
