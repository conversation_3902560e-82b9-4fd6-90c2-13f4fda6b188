import React, { useCallback, useMemo, useState } from "react";
import { CheckBoxActions } from "@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal";
import { SeaSelectInput } from "@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput";
import { sharedState } from "@src/shared-state/shared-state";
import { ViewStyle } from "react-native";
import { VesselFilterDropdownWithoutDivisions } from "./VesselFilterDropdownWithoutDivisions";
import { VesselFilterDropdownWithDivisions } from "./VesselFilterDropdownWithDivisions";

export interface VesselFilterDropdownProps {
  vesselIds?: string[];
  setVesselIds?: React.Dispatch<React.SetStateAction<string[]>>;
  isMulti?: boolean;
  style?: ViewStyle;
  noValidation?: boolean;
}

export const VesselFilterDropdown = ({
  vesselIds = [],
  setVesselIds,
  isMulti = true,
  style,
  noValidation = false,
}: VesselFilterDropdownProps) => {
  const divisions = sharedState.divisions.use();

  if (divisions?.root) {
    return (
      <VesselFilterDropdownWithDivisions
        vesselIds={vesselIds}
        setVesselIds={setVesselIds}
        style={style}
      />
    );
  }

  return (
    <VesselFilterDropdownWithoutDivisions
      vesselIds={vesselIds}
      setVesselIds={setVesselIds}
      isMulti={isMulti}
      style={style}
      noValidation={noValidation}
    />
  );
};
