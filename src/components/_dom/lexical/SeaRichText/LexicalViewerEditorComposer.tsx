"use dom";

import React from "react";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import {
  lexicalTheme,
  lexNodes,
} from "@src/components/_dom/lexical/SeaRichText/SeaRichTextUtils";
import { TableContext } from "@src/components/_dom/lexical/plugins/LexTable/LexTable";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import LexicalErrorBoundary from "@lexical/react/LexicalErrorBoundary";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { TablePlugin } from "@lexical/react/LexicalTablePlugin";
import LexTableCellResizer from "@src/components/_dom/lexical/plugins/LexTableCellResizer/LexTableCellResizer";
import LexImages from "@src/components/_dom/lexical/plugins/LexImages/LexImages";
import LexPagePlugin from "@src/components/_dom/lexical/plugins/LexPage/LexPage";
import { HorizontalRulePlugin } from "@lexical/react/LexicalHorizontalRulePlugin";
import { TabIndentationPlugin } from "@lexical/react/LexicalTabIndentationPlugin";
import { LexicalEditor } from "lexical";

// Styling
import "./SeaRichText.css";
import LexToolBar from "@src/components/_dom/lexical/plugins/LexToolBar/LexToolBar";
import { ScrollView } from "react-native";

const LexicalViewerEditorComposer = ({ children, isEditable = false }) => {
  const onLexicalError = (error: Error, editor: LexicalEditor) => {
    console.error(error);
    console.log("Lexical editor", editor);
  };

  return (
    <div className="document">
      <div className="sfdoc white-zone">
        <LexicalComposer
          initialConfig={{
            // TODO: Change back
            editable: isEditable,
            namespace: "LexEditor",
            theme: lexicalTheme,
            nodes: lexNodes,
            onError: onLexicalError,
            // editorState: content
          }}
        >
          {isEditable && (
            <>
              <LexToolBar level={1} />
            </>
          )}
          {/* <LexDragDropPaste /> */}
          {/* <AutoFocusPlugin /> */}

          <RichTextPlugin
            contentEditable={
              <ScrollView
                key={`companyPlan`}
                style={
                  {
                    // flex: 1,
                  }
                }
                contentContainerStyle={
                  {
                    // flexGrow: 1,
                    // minHeight: "100%",
                  }
                }
                scrollEnabled={true}
                showsVerticalScrollIndicator={true}
              >
                <>
                  {/*<div className="lex-scroller">*/}
                  {/* <div className="lex-editor" ref={onRef}> */}

                  <div className="lex-editor">
                    <ContentEditable className="lex-content-editable-root" />
                  </div>
                  {/*</div>*/}
                </>
              </ScrollView>
            }
            placeholder={null}
            ErrorBoundary={LexicalErrorBoundary}
          />

          <ListPlugin />
          <TablePlugin />
          <LexTableCellResizer />
          <LexImages />
          {/* TODO: Check - duplicated in `SeaRichTextViewer` */}
          <LexPagePlugin />
          <HorizontalRulePlugin />
          <TabIndentationPlugin />

          {children}

          {/* <HistoryPlugin /> */}
          {/* <TreeViewPlugin /> */}
        </LexicalComposer>
      </div>
    </div>
  );
};

export default LexicalViewerEditorComposer;
