import {
  CreateableDocument,
  SharedStateConfig,
  UpdateableDocument,
  sharedState,
} from "@src/shared-state/shared-state";
import { canView } from "@src/shared-state/Core/userPermissions";
import {
  collection,
  onSnapshot,
  orderBy,
  query,
  where,
} from "@src/lib/firebase/services/firestore.service";
import { firestore } from "@src/lib/firebase";
import { getDayOffset, MAX_DATE, warnDays } from "@src/lib/datesAndTime";
import { SFDoc } from "@src/shared-state/CompanyDocuments/companyDocuments";
import {
  registerFiles,
  registerRichText,
} from "@src/shared-state/FileSyncSystem/filesToCache";

export interface VesselDocument extends CreateableDocument, UpdateableDocument {
  categoryId: string;
  deletedBy?: string;
  emailReminder?: string;
  files?: string[];
  interval?: string;
  sfdoc?: SFDoc;
  state: string;
  title: string;
  type: string;
  vesselId: string;
  whenDeleted?: number;
  dateExpires?: string;
  dateToRemind?: string;
}

export type VesselDocumentsData = {
  all: VesselDocument[];
  prioritised: VesselDocument[];
  byId: {
    [id: string]: VesselDocument;
  };
  byCategoryId: {
    [id: string]: VesselDocument[];
  };
};

export const vesselDocumentsConfig: SharedStateConfig<VesselDocumentsData> = {
  isAlwaysActive: false,
  dependencies: ["vesselId", "todayMillis", "userPermissions"], // Depends on todayMillis because we're doing day offset calculations
  countLiveDocs: () => sharedState.vesselDocuments.current?.all.length ?? 0,
  run: (done, set, clear) => {
    clear();
    const vesselId = sharedState.vesselId.current;
    if (vesselId && canView("vesselDocuments")) {
      return onSnapshot(
        query(
          collection(firestore, "vesselDocuments"),
          where("vesselId", "==", vesselId),
          where("state", "==", "active"),
          orderBy("title", "asc")
        ),
        (snap) => {
          done();
          const documents = snap.docs.map((doc) => {
            return {
              id: doc.id,
              ...doc.data(),
            } as VesselDocument;
          });

          // all is categorised by renewable and nonExpiring
          const all = [] as VesselDocument[];
          const byId = {} as {
            [id: string]: VesselDocument;
          };
          documents.forEach((document) => {
            registerFiles(document.files, "vesselDocuments", document);
            registerRichText(document.sfdoc, "vesselDocuments");
            byId[document.id] = document;
            all.push(document);
          });
          let prioritised = [...documents] as VesselDocument[];
          prioritised.sort((a, b) => {
            return (
              a.type === "renewable" ? (a.dateExpires ?? MAX_DATE) : MAX_DATE
            ).localeCompare(
              b.type === "renewable" ? (b.dateExpires ?? MAX_DATE) : MAX_DATE
            );
          });

          // prioritised should only contain dateExpires up to warnDays.vesselDocuments days in the future
          // (and should not contain and nonExpiring either)
          const maxDateExpires = getDayOffset(
            warnDays.vesselDocuments[warnDays.vesselDocuments.length - 1]
          );
          for (let i = 0; i < prioritised.length; i++) {
            if (
              prioritised[i].type === "nonExpiring" ||
              (prioritised[i].dateExpires &&
                prioritised[i].dateExpires! >= maxDateExpires)
            ) {
              prioritised = prioritised.slice(0, i);
              break;
            }
          }

          const byCategoryId = {} as {
            [id: string]: VesselDocument[];
          };

          all.forEach((vesselDocument) => {
            byId[vesselDocument.id] = vesselDocument;
            if (byCategoryId[vesselDocument.categoryId] === undefined) {
              byCategoryId[vesselDocument.categoryId] = [];
            }
            byCategoryId[vesselDocument.categoryId].push(vesselDocument);
          });

          set({
            all,
            prioritised,
            byId,
            byCategoryId,
          });
        },
        (error) => {
          done();
          // This should be very rare
          console.log(
            `Failed to access vesselDocuments for vessel ${vesselId}`,
            error
          );
        }
      );
    } else {
      done();
    }
  },
};
