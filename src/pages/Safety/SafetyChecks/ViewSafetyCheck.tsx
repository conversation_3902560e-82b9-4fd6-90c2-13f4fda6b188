import React, { useEffect, useMemo, useState } from "react";
import { sharedState } from "@src/shared-state/shared-state";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import { SeaPageCard } from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import {
  formatDateShort,
  formatShortTimeDurationHrsMinsView,
  warnDays,
} from "@src/lib/datesAndTime";
import { formatInterval } from "@src/lib/util";
import { renderCategoryName } from "@src/lib/categories";
import { SeaEditButton } from "@src/components/_molecules/IconButtons/SeaEditButton";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { SeaLabelValue } from "@src/components/_atoms/SeaLabelValue/SeaLabelValue";
import { SeaSpacer } from "@src/components/_atoms/SeaSpacer/SeaSpacer";
import { SeaDeleteButton } from "@src/components/_molecules/IconButtons/SeaDeleteButton";
import { WhenDueStatus } from "@src/components/_molecules/WhenDueStatus/WhenDueStatus";
import { renderFullNameForUserId } from "@src/shared-state/Core/users";
import { SafetyCheckHistoryTable } from "@src/components/_organisms/Safety/SafetyChecks/SafetyChecksHistoryTable";
import { useCompletedSafetyCheckItems } from "@src/shared-state/VesselSafety/useCompletedSafetyCheckItems";
import { ModifySafetyCheckDrawer } from "@src/components/_organisms/Safety/SafetyChecks/ModifySafetyCheckDrawer";
import { CompleteSafetyCheckDrawer } from "@src/components/_organisms/Safety/SafetyChecks/CompleteSafetyCheckDrawer";
import { DrawerMode } from "@src/components/_atoms/SeaDrawer/SeaDrawer";
import { useLicenseeSettings } from "@src/hooks/useLicenseeSettings";
import SeaFileImage from "@src/components/_atoms/SeaFileImage/SeaFileImage";
import { useDeviceWidth } from "@src/hooks/useDevice";

const DESKTOP_ITEMS_WIDTH = "60%";

interface ViewSafetyCheckProps {
  itemId: string;
}

const ViewSafetyCheck = ({ itemId }: ViewSafetyCheckProps) => {
  // Global State
  const safetyChecks = sharedState.safetyCheckItems.use();
  const safetyCheckCategories = sharedState.safetyCheckCategories.use();
  const vesselSafetyItems = sharedState.vesselSafetyItems.use();
  const vesselLocations = sharedState.vesselLocations.use();

  // Internal State
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isCompleteModalVisible, setIsCompleteModalVisible] = useState(false);

  // Hooks
  const { hasTimeTrackingEnabled } = useLicenseeSettings();
  const { isMobileWidth } = useDeviceWidth();

  const item = safetyChecks?.byId[itemId];
  const historyItems = useCompletedSafetyCheckItems(item);

  const itemName = useMemo(() => {
    return renderCategoryName(item?.itemId, vesselSafetyItems);
  }, [item, vesselSafetyItems]);
  const isCritical = useMemo(() => {
    return vesselSafetyItems?.byId[itemId]?.isCritical ?? false;
  }, [item, vesselSafetyItems]);

  const onHistoryItemPress = () => {
    alert("Not implemented yet");
  };
  // Loading state
  if (!safetyChecks || !item) {
    return <Text>Loading...</Text>;
  }

  return (
    <RequirePermissions
      role={"safetyEquipmentList"}
      level={permissionLevels.VIEW}
      showDenial={true}
    >
      <ScrollView showsVerticalScrollIndicator={false} style={styles.container}>
        <SeaPageCard
          primaryActionButton={
            <SeaButton
              onPress={() => setIsCompleteModalVisible(true)}
              variant={SeaButtonVariant.Primary}
              label={"Complete"}
              iconOptions={{ icon: "check" }}
            />
          }
          secondaryActionButton={[
            <SeaDeleteButton
              key={"Delete"}
              onPress={() => {
                alert("Coming soon!");
                // setEditIncidentVisible(true);
              }}
            />,
            <SeaEditButton
              key={"Edit"}
              onPress={() => {
                setIsEditModalVisible(true);
              }}
            />,
          ]}
        >
          <SeaStack
            direction={"row"}
            justify={"start"}
            gap={isMobileWidth ? 15 : 20}
          >
            <View style={{ width: isMobileWidth ? 60 : 100 }}>
              <SeaFileImage
                files={item.files}
                size={isMobileWidth ? "tiny" : "medium"}
              />
            </View>
            <SeaStack direction={"column"} justify={"start"} align={"start"}>
              <SeaTypography variant={"title"} textStyle={{ marginBottom: 0 }}>
                {itemName}
              </SeaTypography>
              {item.dateDue ? (
                <WhenDueStatus
                  whenDue={item.dateDue}
                  warnDaysThreshold={warnDays.safetyEquipmentChecks[0]}
                  hasFault={item.hasFault ?? false}
                />
              ) : (
                <></>
              )}
            </SeaStack>
          </SeaStack>
          <SeaSpacer height={30} />
          <SeaStack direction={"column"} align={"start"} gap={10}>
            {/*    /!** Card 1 - START *!/*/}
            <SeaStack
              align={"start"}
              style={{
                width: "100%",
              }}
            >
              <SeaStack
                direction={"column"}
                align={"start"}
                style={{
                  width: "100%",
                }}
              >
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue
                    label={"Location"}
                    iconOptions={{ icon: "location_on" }}
                    showIcon={true}
                    value={renderCategoryName(item.locationId, vesselLocations)}
                  />
                  <SeaLabelValue
                    label={"Category"}
                    iconOptions={{ icon: "category" }}
                    showIcon={true}
                    value={renderCategoryName(
                      item.categoryId,
                      safetyCheckCategories,
                    )}
                  />
                </SeaStack>

                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue
                    label={"Critical Equipment"}
                    iconOptions={{ icon: "flag" }}
                    showIcon={true}
                    value={isCritical ? "Yes" : "No"}
                  />
                  <SeaLabelValue
                    label={"Interval"}
                    iconOptions={{ icon: "update" }}
                    showIcon={true}
                    value={formatInterval(item.interval)}
                  />
                </SeaStack>
              </SeaStack>
            </SeaStack>

            {/** Card 2 - START */}
            <SeaSpacer height={5} />
            <>
              <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                <SeaLabelValue
                  label={"Next Check"}
                  iconOptions={{ icon: "calendar_month" }}
                  showIcon={true}
                  value={formatDateShort(item.dateDue)}
                />
                <SeaLabelValue
                  label={"Assigned To"}
                  iconOptions={{ icon: "person" }}
                  showIcon={true}
                  value={
                    item.assignedTo?.length > 0
                      ? item.assignedTo
                          .map((x) => renderFullNameForUserId(x))
                          .join(", ")
                      : "-"
                  }
                />
                {hasTimeTrackingEnabled && (
                  <SeaLabelValue
                    label={"Estimated Time"}
                    iconOptions={{ icon: "timer" }}
                    showIcon={true}
                    value={
                      item.estimatedTime
                        ? formatShortTimeDurationHrsMinsView(item.estimatedTime)
                        : "-"
                    }
                  />
                )}
              </SeaStack>
            </>

            <SeaSpacer height={5} />
          </SeaStack>
        </SeaPageCard>

        <SeaStack
          direction={"column"}
          justify={"start"}
          align={"start"}
          gap={50}
        >
          <View style={styles.taskView}>
            <SeaTypography variant={"cardTitle"}>Task</SeaTypography>
            <SeaStack direction={"column"} align={"start"}>
              <SeaTypography variant={"value"}>
                {item.description}
              </SeaTypography>
            </SeaStack>
          </View>

          <SafetyCheckHistoryTable
            items={historyItems ?? []}
            hasTimeTrackingEnabled={hasTimeTrackingEnabled}
          />
        </SeaStack>
      </ScrollView>

      {/*<CompleteSafetyEquipmentExpiryDrawer*/}
      <CompleteSafetyCheckDrawer
        selectedItem={item}
        visible={isCompleteModalVisible}
        onClose={() => setIsCompleteModalVisible(false)}
      />

      {isEditModalVisible && (
        <ModifySafetyCheckDrawer
          selectedItemId={item.id}
          mode={DrawerMode.Edit}
          visible={isEditModalVisible}
          onClose={() => setIsEditModalVisible(false)}
        />
      )}
    </RequirePermissions>
  );
};

const styles = StyleSheet.create({
  container: {
    height: "100%",
  },
  taskView: {
    paddingHorizontal: 20,
  },
});

export default ViewSafetyCheck;
