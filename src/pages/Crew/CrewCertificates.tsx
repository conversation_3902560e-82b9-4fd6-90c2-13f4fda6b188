import { View } from "react-native";
import React, { useEffect, useMemo, useState } from "react";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { ScrollView } from "react-native-gesture-handler";
import { SeaPageCard } from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import { SeaSettingsButton } from "@src/components/_molecules/IconButtons/SeaSettingsButton";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import {
  formatDateShort,
  getDateDifferenceInDays,
  warnDays,
} from "@src/lib/datesAndTime";
import { WhenDueStatus } from "@src/components/_molecules/WhenDueStatus/WhenDueStatus";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";
import {
  SeaFilterTag,
  SeaFilterTags,
  SeaFilterTagsValue,
} from "@src/components/_atoms/SeaFilterTags/SeaFilterTags";
import { sharedState } from "@src/shared-state/shared-state";
import { DateTime } from "luxon";
import { useRouter } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { SeaTableIcon } from "@src/components/_atoms/SeaTable/SeaTableIcon";
import { DrawerMode } from "@src/components/_atoms/SeaDrawer/SeaDrawer";
import {
  CertificateHolderInfo,
  CrewCertificate,
} from "@src/shared-state/Crew/crewCertificates";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { SeaFilterSearch } from "@src/components/_atoms/SeaFilterSearch/SeaFilterSearch";
import { SeaStatusPill } from "@src/components/_atoms/SeaStatusPill/SeaStatusPill";
import { SeaStatusType } from "@src/types/Common";
import { VesselFilterDropdown } from "@src/components/_molecules/VesselFilterDropdown/VesselFilterDropdown";
import { useCrewSubNav } from "@src/hooks/useSubNav";
import { EditCrewCertificateDrawer } from "@src/components/_organisms/Crew/CrewCertificates/EditCrewCertificateDrawer";
import { SeaTableImage } from "@src/components/_atoms/SeaTable/SeaTableImage";

// [imports remain unchanged]

export function CrewCertificates() {
  const vessel = sharedState.vessel.use();
  const vesselId = sharedState.vesselId.use();
  const vesselIds = sharedState.vesselIds.use();
  const crewCertificates = sharedState.crewCertificates.use();
  const users = sharedState.users.use();

  sharedState.crewCertificateTitles.use();

  const { styles } = useStyles(styleSheet);
  const router = useRouter();

  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(false);
  const [seaFilterTagsValue, setSeaFilterTagsValue] = useState<
    Partial<SeaFilterTagsValue>
  >({
    all: { isActive: false },
    overdue: { isActive: true },
    upcoming: { isActive: true },
  });
  const [searchFilters, setSearchFilters] = useState({
    searchValue: "",
    gapAnalysisEnabled: false,
    gapAnalysisCount: 0,
    operationalCrewOnly: false,
  });

  const [filterVesselIds, setFilterVesselIds] = useState<string[]>([]);
  const [selectedMissingItem, setSelectedMissingItem] =
    useState<CrewCertificate>();

  const { allData, overDueData, upcomingData, gapAnalysisData } =
    useMemo(() => {
      const allData: CrewCertificate[] = [];
      const overDueData: CrewCertificate[] = [];
      const upcomingData: CrewCertificate[] = [];
      const gapAnalysisData: CrewCertificate[] = [];

      const query = searchFilters.searchValue?.toLowerCase() ?? "";

      for (const holder of crewCertificates?.holdersSorted ?? []) {
        const certificates = holder.certificates ?? [];

        for (const cert of certificates) {
          const user = users?.byId[cert.heldBy];

          if (
            searchFilters.operationalCrewOnly &&
            !user?.crewVesselIds?.some?.((id) => filterVesselIds?.includes(id))
          ) {
            continue;
          }

          if (query) {
            const title = cert.title?.toLowerCase() ?? "";
            const searchText = cert.searchText?.split(" ") ?? [];
            const matches =
              title.includes(query) ||
              searchText.some((text) => text.toLowerCase().includes(query));

            if (!matches) continue;
          }

          allData.push(cert);

          const diff = cert.dateExpires
            ? getDateDifferenceInDays(DateTime.fromISO(cert.dateExpires))
            : 0;

          if (diff < 0) overDueData.push(cert);
          if (diff > 0 && diff < warnDays.vesselCertificates[0])
            upcomingData.push(cert);

          if (
            cert.state === "missing" ||
            (cert.type === "renewable" &&
              (diff < 0 || (diff > 0 && diff < warnDays.vesselCertificates[0])))
          ) {
            gapAnalysisData.push(cert);
          }
        }
      }

      return { allData, overDueData, upcomingData, gapAnalysisData };
    }, [
      crewCertificates,
      warnDays,
      searchFilters.searchValue,
      searchFilters.operationalCrewOnly,
      users,
      filterVesselIds,
    ]);

  // 🔁 Update counts in filter tags
  useEffect(() => {
    setSeaFilterTagsValue((prev) => ({
      ...prev,
      all: {
        ...prev.all,
        count: allData.length,
        isActive: prev.all?.isActive ?? false,
      },
      overdue: {
        ...prev.overdue,
        count: overDueData.length,
        isActive: prev.overdue?.isActive ?? false,
      },
      upcoming: {
        ...prev.upcoming,
        count: upcomingData.length,
        isActive: prev.upcoming?.isActive ?? false,
      },
    }));
  }, [allData, overDueData, upcomingData]);

  // 🔁 Update gapAnalysisCount
  useEffect(() => {
    setSearchFilters((prev) => ({
      ...prev,
      gapAnalysisCount: gapAnalysisData.length,
    }));
  }, [gapAnalysisData]);

  const filteredTasks = useMemo(() => {
    if (!crewCertificates) return [];

    if (searchFilters.gapAnalysisEnabled) return gapAnalysisData;
    if (seaFilterTagsValue.all?.isActive) return allData;

    const result: CrewCertificate[] = [];

    if (seaFilterTagsValue.overdue?.isActive) {
      result.push(...overDueData);
    }

    if (seaFilterTagsValue.upcoming?.isActive) {
      result.push(...upcomingData);
    }

    const unique = new Map(result.map((item) => [item.id, item]));
    return Array.from(unique.values());
  }, [
    allData,
    overDueData,
    upcomingData,
    gapAnalysisData,
    seaFilterTagsValue,
    searchFilters.gapAnalysisEnabled,
    crewCertificates,
  ]);

  const handlePress = (item: CrewCertificate) => {
    if (item.state === "missing") {
      setIsVisibleAddDrawer(true);
      setSelectedMissingItem(item);
    } else {
      router.navigate({
        pathname: getRoutePath(Routes.CREW_CERTIFICATES_VIEW),
        params: {
          vesselId: vessel?.id,
          certificateId: item.id,
        },
      });
    }
  };

  const columns = useMemo(
    () =>
      buildColumns(seaFilterTagsValue.all?.isActive, crewCertificates?.holders),
    [vessel, crewCertificates, seaFilterTagsValue.all?.isActive],
  );

  const rows = useMemo(
    () => buildRows(filteredTasks, handlePress, crewCertificates?.holders),
    [filteredTasks, crewCertificates],
  );

  useEffect(() => {
    if (vesselId) {
      setFilterVesselIds([vesselId]);
    } else {
      setFilterVesselIds(vesselIds ?? []);
    }
  }, [vesselId, vesselIds]);

  return (
    <>
      <ScrollView style={styles.container}>
        <SeaPageCard
          title="Crew Certificates"
          primaryActionButton={
            <RequirePermissions
              role="crewCertificates"
              level={permissionLevels.CREATE}
            >
              <SeaButton
                onPress={() => setIsVisibleAddDrawer(true)}
                variant={SeaButtonVariant.Secondary}
                label={"Add New"}
                iconOptions={{ icon: "add" }}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <SeaDownloadButton
              key={"download"}
              onPress={() => alert("This functionality is not completed yet")}
            />,
            <RequirePermissions
              key={"settings"}
              role="crewCertificates"
              level={permissionLevels.CREATE}
            >
              <SeaSettingsButton
                onPress={() => alert("This functionality is not completed yet")}
              />
            </RequirePermissions>,
          ]}
          subNav={useCrewSubNav(vesselId, Routes.CREW_CERTIFICATES)}
        />

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{
            width: "100%",
            gap: 10,
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <SeaFilterTags
            value={seaFilterTagsValue}
            onChange={(value) => {
              setSearchFilters((prev) => ({
                ...prev,
                gapAnalysisEnabled: false,
              }));
              setSeaFilterTagsValue(value);
            }}
          />

          <SeaStack direction="row" gap={8} align="center">
            <VesselFilterDropdown
              vesselIds={filterVesselIds}
              setVesselIds={setFilterVesselIds}
              noValidation
            />

            <SeaFilterTag
              label="Operational Crew Only"
              onToggle={() =>
                setSearchFilters((prev) => ({
                  ...prev,
                  operationalCrewOnly: !prev.operationalCrewOnly,
                }))
              }
              enabled={searchFilters.operationalCrewOnly}
              showCount={false}
            />

            <SeaFilterTag
              label="Gap Analysis"
              onToggle={() =>
                setSearchFilters((prev) => {
                  const gapAnalysisEnabled = !prev.gapAnalysisEnabled;

                  setSeaFilterTagsValue((prev) => ({
                    ...prev,
                    all: { ...prev.all, isActive: false },
                    overdue: {
                      ...prev.overdue,
                      isActive: !gapAnalysisEnabled,
                    },
                    upcoming: {
                      ...prev.upcoming,
                      isActive: !gapAnalysisEnabled,
                    },
                  }));

                  return {
                    ...prev,
                    gapAnalysisEnabled,
                  };
                })
              }
              enabled={searchFilters.gapAnalysisEnabled}
              count={searchFilters.gapAnalysisCount}
            />
            <SeaFilterSearch
              value={searchFilters.searchValue}
              onChangeText={(value) =>
                setSearchFilters((prev) => ({ ...prev, searchValue: value }))
              }
              noValidation
            />
          </SeaStack>
        </ScrollView>

        <View style={styles.tableView}>
          <ScrollView>
            <SeaTable
              columns={columns}
              rows={rows}
              showGroupedTable={
                seaFilterTagsValue.all?.isActive ||
                searchFilters.gapAnalysisEnabled
                  ? true
                  : false
              }
            />
          </ScrollView>
        </View>
      </ScrollView>

      {isVisibleAddDrawer && (
        <EditCrewCertificateDrawer
          onClose={() => {
            setSelectedMissingItem(undefined);
            setIsVisibleAddDrawer(false);
          }}
          visible={isVisibleAddDrawer}
          type={DrawerMode.Create}
          selectedItem={selectedMissingItem}
        />
      )}
    </>
  );
}

const buildColumns = (
  hideHeldBy = false,
  certificateHolder?: Record<string, CertificateHolderInfo>,
) => {
  return [
    {
      label: "",
      render: (row: CrewCertificate) => <SeaTableImage files={row.files} />,
      width: 50,
    },
    {
      label: "Certificate",
      value: (row: CrewCertificate) => row.title,
    },
    ...(!hideHeldBy
      ? [
          {
            label: "Held By",
            value: (row: CrewCertificate) =>
              certificateHolder?.[row.heldBy]?.name ?? "",
            icon: () => <SeaTableIcon icon={"person"} />,
            compactModeOptions: {
              hideRow: true,
            },
          },
        ]
      : []),
    {
      label: "Issued By",
      value: (row: CrewCertificate) => row.issuedBy,
      icon: () => <SeaTableIcon icon={"fact_check"} />,
    },
    {
      label: "Expiry",
      value: (row: CrewCertificate) => formatDateShort(row.dateExpires),
      icon: () => <SeaTableIcon icon={"calendar_month"} />,
    },
    {
      label: "Status",
      render: (row: CrewCertificate) =>
        row.state === "missing" ? (
          <SeaStatusPill
            primaryLabel={"MISSING"}
            variant={SeaStatusType.Attention}
          />
        ) : row.dateExpires ? (
          <WhenDueStatus
            whenDue={row.dateExpires}
            warnDaysThreshold={warnDays.crewCertificates[0]}
            hasFault={false}
          />
        ) : (
          <></>
        ),
      width: 150,
    },
    {
      label: "Reminder",
      render: (row: CrewCertificate) =>
        row.emailReminder ? (
          <SeaIcon icon="email" size={20} fill={false} />
        ) : null,
      compactModeOptions: {
        hideRow: true,
      },
    },
  ] as SeaTableColumn<CrewCertificate>[];
};

const buildRows = (
  items: CrewCertificate[],
  onPress: (item: CrewCertificate) => void,
  certificateHolder?: Record<string, CertificateHolderInfo>,
) => {
  return items.map((item) => ({
    data: item,
    onPress: (item: CrewCertificate) => onPress(item),
    group: (item: CrewCertificate) =>
      certificateHolder?.[item.heldBy]?.name ?? "Unknown",
  }));
};

const styleSheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}));
