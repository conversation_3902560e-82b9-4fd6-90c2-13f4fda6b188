import {
  SeaFilterTags,
  SeaFilterTagsValue,
} from "@src/components/_atoms/SeaFilterTags/SeaFilterTags";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import { MaintenanceFilters } from "@src/components/_organisms/Maintenance/MaintenanceSchedule/MaintenanceFilters";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { CategoriesData, renderCategoryName } from "@src/lib/categories";
import { formatValue, truncateText } from "@src/lib/util";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import { sharedState } from "@src/shared-state/shared-state";
import { Equipment } from "@src/shared-state/VesselMaintenance/equipment";
import { colors } from "@src/theme/colors";
import React, { useCallback, useMemo, useState } from "react";
import { ScrollView, StyleSheet, View } from "react-native";
import { SeaSettingsButton } from "@src/components/_molecules/IconButtons/SeaSettingsButton";
import { SeaDownloadButton } from "@src/components/_molecules/IconButtons/SeaDownloadButton";
import {
  SeaPageCard,
  SubNav,
} from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { usePermission } from "@src/hooks/usePermission";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { useRouter } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { Routes } from "@src/navigation/constants";
import { EditEquipmentListDrawer } from "@src/components/_organisms/Maintenance/EquipmentList/EditEquipmentListDrawer";
import { ScrollablePageLayout } from "@src/layout/ScrollablePageLayout/ScrollablePageLayout";
import { SeaTableImage } from "@src/components/_atoms/SeaTable/SeaTableImage";

const EquipmentListPermissions = {
  maintenanceSchedule: { level: permissionLevels.CREATE },
};

interface EquipmentListProps {
  headerSubNavigation?: SubNav[];
}

export const EquipmentList = ({
  headerSubNavigation = undefined,
}: EquipmentListProps) => {
  const vesselId = sharedState.vesselId.use();

  const vesselLocations = sharedState.vesselLocations.use();
  const equipment = sharedState.equipment.use();
  const vesselSystems = sharedState.vesselSystems.use();

  const router = useRouter();

  const [seaFilterTagsValue, setSeaFilterTagsValue] = useState<
    Partial<SeaFilterTagsValue>
  >({
    all: { isActive: true },
    critical: { isActive: false },
  });
  const [searchValue, setSearchValue] = useState("");
  const [systemFilter, setSystemFilter] = useState("");
  const [locationFilter, setLocationFilter] = useState("");
  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(false);

  const modulePermissions = usePermission<typeof EquipmentListPermissions>({
    modules: EquipmentListPermissions,
  });

  const systemFilterFilterOptions = useMemo(() => {
    if (!equipment?.filterOptions) return [];

    const options = equipment.filterOptions.systemIds.map((id: string) => ({
      label: renderCategoryName(id, vesselSystems),
      value: id,
    }));

    return [
      {
        label: "All",
        value: "",
      },
      ...options,
    ];
  }, [equipment]);

  const locationFilterOptions = useMemo(() => {
    if (!equipment?.filterOptions) return [];
    const options = equipment.filterOptions.locationIds.map((id: string) => ({
      label: renderCategoryName(id, vesselLocations),
      value: id,
    }));

    return [
      {
        label: "All",
        value: "",
      },
      ...options,
    ];
  }, [equipment, vesselLocations]);

  const allData = useMemo(() => {
    const data = equipment?.all;

    setSeaFilterTagsValue((prev) => ({
      ...prev,
      all: { isActive: prev.all?.isActive ?? false, count: data?.length ?? 0 },
    }));

    return data;
  }, [equipment]);

  const criticalData = useMemo(() => {
    const data = equipment?.all.filter((item) => item.isCritical);

    setSeaFilterTagsValue((prev) => ({
      ...prev,
      critical: {
        isActive: prev.critical?.isActive ?? false,
        count: data?.length ?? 0,
      },
    }));

    return data;
  }, [equipment]);

  const filteredEquipments = useMemo(() => {
    if (!equipment) return undefined;

    let filteredEquipment: Equipment[] = [];

    // Apply filters based on the selected filter tags
    if (seaFilterTagsValue.all?.isActive) {
      filteredEquipment = allData ?? [];
    } else {
      filteredEquipment = criticalData ?? [];
    }

    // Filter by system
    if (systemFilter) {
      filteredEquipment = filteredEquipment.filter(
        (equipment) => equipment?.systemId === systemFilter,
      );
    }

    //Filter by location
    if (locationFilter) {
      filteredEquipment = filteredEquipment.filter(
        (equipment) => equipment.locationId === locationFilter,
      );
    }

    if (searchValue) {
      filteredEquipment = filteredEquipment.filter((equipment) => {
        return equipment.searchText?.includes(searchValue.toLowerCase());
      });
    }

    return filteredEquipment;
  }, [
    allData,
    criticalData,
    seaFilterTagsValue,
    searchValue,
    systemFilter,
    locationFilter,
  ]);

  const handleRow = useCallback(
    (item: Equipment) => {
      router.navigate({
        pathname: getRoutePath(Routes.EQUIPMENT_LIST_VIEW),
        params: {
          vesselId: vesselId,
          equipmentId: item.id,
        },
      });
    },
    [vesselId],
  );

  const columns = useMemo(
    () => buildColumns(vesselLocations),
    [vesselLocations],
  );
  const rows = useMemo(
    () =>
      buildRows(
        filteredEquipments ?? [],
        (item: Equipment) => handleRow(item),
        vesselSystems,
      ),
    [filteredEquipments, vesselSystems],
  );

  return (
    <ScrollablePageLayout>
      <RequirePermissions
        role="maintenanceSchedule"
        level={permissionLevels.VIEW}
        showDenial={true}
      >
        <SeaPageCard
          title={"Equipment List"}
          primaryActionButton={
            modulePermissions.maintenanceSchedule ? (
              <SeaButton
                onPress={() => setIsVisibleAddDrawer(true)}
                variant={SeaButtonVariant.Primary}
                label={"Add New"}
                iconOptions={{ icon: "add" }}
              />
            ) : undefined
          }
          secondaryActionButton={[
            <SeaDownloadButton
              key={"download"}
              onPress={() => alert("This functionality is not completed yet")}
            />,
            <SeaSettingsButton
              key={"settings"}
              onPress={() => alert("This functionality is not completed yet")}
            />,
          ]}
          subNav={headerSubNavigation}
        />

        <View
          style={{
            width: "100%",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <ScrollView
            horizontal
            style={{
              width: "100%",
              display: "flex",
              flexDirection: "column",
              paddingVertical: 10,
            }}
          >
            <SeaStack
              direction="row"
              justify="between"
              gap={10}
              style={{ width: "100%" }}
            >
              {/* Filter Tags */}
              <SeaFilterTags
                value={seaFilterTagsValue}
                onChange={(value) => setSeaFilterTagsValue(value)}
              />

              {/* Filter Row */}
              <MaintenanceFilters
                searchFilter={{
                  searchValue: searchValue,
                  setSearchValue: setSearchValue,
                }}
                systemFilter={{
                  value: systemFilter,
                  setValue: setSystemFilter,
                  options: systemFilterFilterOptions,
                }}
                locationFilter={{
                  value: locationFilter,
                  setValue: setLocationFilter,
                  options: locationFilterOptions,
                }}
                setSeaFilterTagsValue={() =>
                  setSeaFilterTagsValue({
                    all: { isActive: true },
                    critical: { isActive: false },
                  })
                }
              />
            </SeaStack>
          </ScrollView>
        </View>

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable columns={columns} showGroupedTable rows={rows} />
        </View>
        {isVisibleAddDrawer && (
          <EditEquipmentListDrawer
            onClose={() => setIsVisibleAddDrawer(false)}
            visible={isVisibleAddDrawer}
            type="create"
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  );
};

const buildRows = (
  items: Equipment[],
  onPress: (item: Equipment) => void,
  vesselSystems?: CategoriesData,
) => {
  return items.map((item) => ({
    data: item,
    onPress: (item: Equipment) => onPress(item),
    group: (item: Equipment) =>
      renderCategoryName(item.systemId, vesselSystems),
  }));
};

const buildColumns = (vesselLocations?: CategoriesData) => {
  return [
    {
      label: "",
      render: (item) => <SeaTableImage files={item.files} />,
    },
    {
      label: "Equipment",
      value: (item) => `${truncateText(item.equipment)}`,
      style: { fontWeight: "bold" },
    },
    {
      label: "Location",
      value: (item) => {
        return formatValue(
          renderCategoryName(item.locationId, vesselLocations),
        );
      },
    },
    {
      label: "Make",
      value: (item) => item.make,
    },
    {
      label: "Model",
      value: (item) => item.model,
    },
    {
      label: "Serial #",
      value: (item) => item.serial,
    },
    {
      label: "Critical",
      value: (item) => item?.isCritical,
      render: (item) => (
        <>
          {item?.isCritical ? (
            <SeaIcon icon={"flag"} color={colors.status.critical} />
          ) : null}
        </>
      ),
    },
  ] as SeaTableColumn<Equipment>[];
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  filterRow: {
    // backgroundColor: "magenta",
  },
  title: {
    // backgroundColor: "magenta"
  },
  actions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  tableView: {
    marginTop: 16,
  },
});
