import React, { useMemo } from "react";
import {
  SeaPageCard,
  SubNav,
} from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import { ScrollablePageLayout } from "@src/layout/ScrollablePageLayout/ScrollablePageLayout";
import { RequirePermissions } from "@src/components/hoc/RequirePermissions";
import { permissionLevels } from "@src/shared-state/Core/userPermissions";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaSettingsButton } from "@src/components/_molecules/IconButtons/SeaSettingsButton";
import { sharedState } from "@src/shared-state/shared-state";
import { VesselDocument } from "@src/shared-state/VesselDocuments/vesselDocuments";
import {
  SeaTable,
  SeaTableColumn,
} from "@src/components/_atoms/SeaTable/SeaTable";
import SeaFileImage from "@src/components/_atoms/SeaFileImage/SeaFileImage";
import { SeaTableIcon } from "@src/components/_atoms/SeaTable/SeaTableIcon";
import {
  formatDateShort,
  formatInterval,
  warnDays,
} from "@src/lib/datesAndTime";
import { WhenDueStatus } from "@src/components/_molecules/WhenDueStatus/WhenDueStatus";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";
import { View } from "react-native";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import { getRoutePath } from "@src/navigation/utils";
import { CategoriesData, renderCategoryName } from "@src/lib/categories";
import { useRouter } from "expo-router";
import { Routes } from "@src/navigation/constants";

interface VesselDocumentsProps {
  headerSubNavigation?: SubNav[];
}

export function VesselDocuments({ headerSubNavigation }: VesselDocumentsProps) {
  const vessel = sharedState.vessel.use();
  const vesselDocuments = sharedState.vesselDocuments.use();
  const vesselDocumentCategories = sharedState.vesselDocumentCategories.use();

  const { styles } = useStyles(styleSheet);
  const router = useRouter();

  const data = useMemo(() => {
    if (!vesselDocuments) return [];

    return vesselDocuments.all;
  }, [vesselDocuments]);

  const handlePress = (item: VesselDocument) => {
    router.navigate({
      pathname: getRoutePath(Routes.VESSEL_DOCUMENTS_VIEW),
      params: {
        vesselId: vessel?.id,
        documentId: item.id,
      },
    });
  };

  const columns = useMemo(() => buildColumns(), [vessel]);
  const rows = useMemo(
    () =>
      buildRows(data, (item) => handlePress(item), vesselDocumentCategories),
    [data, vesselDocumentCategories]
  );

  return (
    <ScrollablePageLayout>
      <RequirePermissions
        role="vesselDocuments"
        level={permissionLevels.VIEW}
        showDenial={true}
      >
        <SeaPageCard
          title="Vessel Documents"
          primaryActionButton={
            <RequirePermissions
              role="vesselDocuments"
              level={permissionLevels.CREATE}
            >
              <SeaButton
                // onPress={() => setIsVisibleAddDrawer(true)}
                onPress={() => alert("This functionality is not completed yet")}
                variant={SeaButtonVariant.Secondary}
                label={"Add New"}
                iconOptions={{ icon: "add" }}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <RequirePermissions
              key={"settings"}
              role="vesselDocuments"
              level={permissionLevels.CREATE}
            >
              <SeaSettingsButton
                onPress={() => alert("This functionality is not completed yet")}
              />
            </RequirePermissions>,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable columns={columns} rows={rows} showGroupedTable />
        </View>

        {/* {isVisibleAddDrawer && (
          <EditVesselCertificateDrawer
            onClose={() => setIsVisibleAddDrawer(false)}
            visible={isVisibleAddDrawer}
            type={DrawerMode.Create}
          />
        )} */}
      </RequirePermissions>
    </ScrollablePageLayout>
  );
}

const buildColumns = () => {
  return [
    {
      label: "",
      render: (row: VesselDocument) => (
        <SeaFileImage files={row.files} size="tiny" />
      ),
      width: 50,
    },
    {
      label: "Name",
      value: (row: VesselDocument) => row.title,
    },
    {
      label: "Expiry",
      value: (row: VesselDocument) => formatDateShort(row.dateExpires),
      icon: () => <SeaTableIcon icon={"calendar_month"} />,
    },
    {
      label: "Interval",
      value: (row: VesselDocument) => formatInterval(row.interval),
      icon: (row: VesselDocument) =>
        row.interval ? <SeaTableIcon icon={"calendar_month"} /> : <></>,
    },
    {
      label: "Status",
      render: (row: VesselDocument) =>
        row.dateExpires ? (
          <WhenDueStatus
            whenDue={row.dateExpires}
            warnDaysThreshold={warnDays.vesselDocuments[0]}
            hasFault={false}
          />
        ) : (
          <></>
        ),
      width: 150,
    },
    {
      label: "Reminder",
      render: (row: VesselDocument) =>
        row.emailReminder ? (
          <SeaIcon icon="email" size={20} fill={false} />
        ) : null,
    },
  ] as SeaTableColumn<VesselDocument>[];
};

const buildRows = (
  items: VesselDocument[],
  onPress: (item: VesselDocument) => void,
  vesselDocumentCategories?: CategoriesData
) => {
  return items.map((item) => ({
    data: item,
    onPress: (item: VesselDocument) => onPress(item),
    group: (item: VesselDocument) =>
      renderCategoryName(item.categoryId, vesselDocumentCategories),
  }));
};

const styleSheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}));
