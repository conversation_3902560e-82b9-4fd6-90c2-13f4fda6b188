import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { ScrollView, Text, View } from "react-native";

import { sharedState } from "@src/shared-state/shared-state";
import { Routes } from "@src/navigation/constants";
import { useNavigation, useRouter } from "expo-router";
import { getRoutePath } from "@src/navigation/utils";
import { FleetCard } from "./FleetCard";
import {
  SeaPageCard,
  SubNavType,
} from "@src/components/_molecules/SeaPageCard/SeaPageCard";
import {
  SeaButton,
  SeaButtonVariant,
} from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { useDeviceWidth } from "@src/hooks/useDevice";
import { SeaFilterSearch } from "@src/components/_atoms/SeaFilterSearch/SeaFilterSearch";
import { SeaSpacer } from "@src/components/_atoms/SeaSpacer/SeaSpacer";
import { VesselFilterDropdown } from "@src/components/_molecules/VesselFilterDropdown/VesselFilterDropdown";

export const FleetDashboard = () => {
  const user = sharedState.user.use();
  const vessels = sharedState.vessels.use();
  const vesselId = sharedState.vesselId.use();
  const vesselIds = sharedState.vesselIds.use();
  const { isMobileWidth } = useDeviceWidth();

  const [searchText, setSearchText] = useState("");
  const [containerWidth, setContainerWidth] = useState(0);
  const [filterVesselIds, setFilterVesselIds] = useState<string[]>([]);

  const router = useRouter();
  const navigation = useNavigation();

  const onPress = useCallback(
    (vesselId: string) => {
      // Also reset the navigation stacks
      navigation.reset({
        index: 0,
        routes: [],
      });

      return router.replace({
        pathname: getRoutePath(Routes.VESSEL_DASHBOARD),
        params: {
          vesselId: vesselId,
        },
      });
    },
    [router],
  );

  const dashboardData = useMemo(() => {
    if (!vessels) return [];

    let filteredVessels = vessels.all;

    if (searchText) {
      return filteredVessels.filter((vessel) =>
        vessel.name.toLowerCase().includes(searchText.toLowerCase()),
      );
    }

    if (filterVesselIds) {
      filteredVessels = vessels.all.filter((vessel) =>
        filterVesselIds.includes(vessel.id),
      );
    }

    return filteredVessels;
  }, [vessels, searchText, filterVesselIds]);

  useEffect(() => {
    setFilterVesselIds(vesselIds ?? []);
  }, [vesselIds]);

  return (
    <ScrollView
      onLayout={(event) => setContainerWidth(event.nativeEvent.layout.width)}
      style={{ flex: 1 }}
    >
      <SeaPageCard
        title={`Hi ${user?.firstName}`}
        primaryActionButton={
          <SeaButton
            label="Fleet Settings"
            variant={SeaButtonVariant.Secondary}
            iconOptions={{
              icon: "settings",
              size: 20,
              fill: false,
            }}
          />
        }
        subNav={
          vesselId
            ? [
                // {
                //   title: "Start New Voyage",
                //   onPress: () =>
                //     router.navigate({
                //       pathname: getRoutePath(Routes.VOYAGE),
                //       params: {
                //         vesselId: vesselId,
                //       },
                //     }),
                //   iconOptions: {
                //     icon: "directions_boat",
                //     size: 20,
                //     fill: false,
                //   },
                // },
                {
                  title: "Log Risk Assessment",
                  onPress: () =>
                    router.navigate({
                      pathname: getRoutePath(Routes.RISK_ASSESSMENT),
                      params: {
                        vesselId: vesselId,
                        openForm: "true",
                      },
                    }),
                  iconOptions: {
                    icon: "warning",
                    size: 20,
                    fill: false,
                  },
                },
                {
                  title: "Complete Form",
                  onPress: () =>
                    router.navigate({
                      pathname: getRoutePath(Routes.CUSTOM_FORMS),
                      params: {
                        vesselId: vesselId,
                        openForm: "true",
                      },
                    }),
                  iconOptions: {
                    icon: "list_alt",
                    size: 20,
                    fill: false,
                  },
                },
                {
                  title: "Log Incident",
                  onPress: () =>
                    router.navigate({
                      pathname: getRoutePath(Routes.INCIDENT_REPORT),
                      params: {
                        vesselId: vesselId,
                        openForm: "true",
                      },
                    }),
                  iconOptions: {
                    icon: "emergency_home",
                    size: 20,
                    fill: false,
                  },
                },
              ]
            : undefined
        }
        subNavType={SubNavType.Button}
        hideBreadcrumbs={true}
      />

      <ScrollView
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        style={{ flex: 1, width: "100%" }}
      >
        <SeaStack
          direction="row"
          gap={8}
          align="center"
          style={{
            marginBottom: 16,
            paddingHorizontal: isMobileWidth ? 10 : 0,
          }}
        >
          <SeaFilterSearch
            value={searchText}
            onChangeText={(value) => setSearchText(value)}
            placeholderText="Search vessel"
            inputContainerStyle={{
              borderRadius: 20,
            }}
            style={{
              width: 200,
            }}
            noValidation
          />
          <View style={{ width: 200 }}>
            <VesselFilterDropdown
              vesselIds={filterVesselIds}
              setVesselIds={setFilterVesselIds}
              noValidation
            />
          </View>
        </SeaStack>
      </ScrollView>
      <SeaStack
        direction={isMobileWidth ? "column" : "row"}
        gap={16}
        style={{
          flexWrap: isMobileWidth ? "nowrap" : "wrap",
        }}
      >
        {dashboardData?.map((vessel) => {
          return (
            <FleetCard
              key={vessel.id}
              vessel={vessel}
              containerWidth={containerWidth}
              onPress={() => onPress(vessel.id)}
            />
          );
        })}
      </SeaStack>
      <SeaSpacer height={100} />
    </ScrollView>
  );
};
