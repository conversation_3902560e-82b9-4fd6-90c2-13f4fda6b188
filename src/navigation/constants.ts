import { IconVariant } from "@src/components/_atoms/SeaIcon/SeaIcon";
import { MaterialIconName } from "@src/types/MaterialIcons";

export type RouteConfig = {
  name: string;
  title: string;
  path: string;
  icon?: { name: MaterialIconName; variant?: IconVariant; size?: number };
  children?: Partial<RoutesConfig>;
};

export type RoutesConfig = Record<Routes, Partial<RouteConfig>>;

/**
 * List of all the routes
 */
export enum Routes {
  INDEX = "index",
  LOGIN = "login",
  LOGOUT = "logout",
  NETWORK = "network",
  HOME = "(home)",
  AUTH = "(auth)",
  ADMIN = "(admin)",
  FLEET_DASHBOARD = "fleet-dashboard",
  VESSEL_DASHBOARD = "vessel-dashboard",
  LOGBOOK = "logbook",

  //Parent routes
  VESSEL = "vessel",
  MAINTENANCE = "maintenance",
  SAFETY = "safety",
  VESSEL_DOCUMENT_REGISTER = "vessel-document-register",
  CREW = "crew",
  HEALTH_SAFETY = "healthSafety",

  // Safety Section
  SAFETY_DASHBOARD = "safety-dashboard",
  SAFETY_EQUIPMENT_CHECKS = "safetyEquipmentChecks",
  SAFETY_EQUIPMENT_CHECKS_LIST = "safetyEquipmentChecksList",
  SAFETY_EQUIPMENT_CHECKS_VIEW = "safetyEquipmentChecksView",
  SAFETY_EQUIPMENT_EXPIRIES = "safetyEquipmentExpiries",

  DRILLS = "drills",
  DRILLS_VIEW_USER_DRILLS = "drillsViewUserDrills",
  DRILLS_VIEW_USER_DRILL = "drillsViewUserDrill",
  DRILLS_VIEW_DRILL = "drillsViewDrill",
  DRILLS_VIEW_DRILL_REPORT = "drillsViewDrillReport",
  DRILLS_VIEW_DRILLS_HISTORY = "drillsViewDrillsHistory",

  SAFETY_EQUIPMENT_EXPIRIES_VIEW = "safetyEquipmentExpiriesList",

  // Maintenance Section
  MAINTENANCE_DASHBOARD = "maintenance-dashboard",

  MAINTENANCE_SCHEDULE = "maintenanceSchedule",
  MAINTENANCE_SCHEDULE_VIEW = "maintenanceScheduleView",

  JOBLIST = "jobList",
  JOBLIST_VIEW = "jobListView",

  SPARE_PARTS_LIST = "sparePartsList",
  SPARE_PARTS_LIST_VIEW = "sparePartsListView",

  EQUIPMENT_LIST = "equipmentList",
  EQUIPMENT_LIST_VIEW = "equipmentListView",

  EQUIPMENT_MANUALS = "equipmentManuals",
  EQUIPMENT_MANUALS_VIEW = "equipmentManualsView",

  MAINTENANCE_HISTORY = "maintenanceHistory",
  MAINTENANCE_HISTORY_VIEW = "maintenanceHistoryView",
  // Vessel Document Register
  VESSEL_DOCUMENT_REGISTER_DASHBOARD = "vessel-document-register-dashboard",
  VESSEL_CERTIFICATES = "vesselCertificates",
  VESSEL_CERTIFICATES_VIEW = "vesselCertificatesView",
  VESSEL_DOCUMENTS = "vesselDocuments",
  VESSEL_DOCUMENTS_VIEW = "vesselDocumentsView",
  SURVEY_DOCUMENTS = "surveyDocuments",
  STANDARD_OPERATING_PROCEDURES = "standardOperatingProcedures",

  // Health & Safety
  HEALTH_SAFETY_DASHBOARD = "health-safety-dashboard",
  INCIDENT_REPORT = "incidentReport",
  RISK_ASSESSMENT = "riskAssessment",
  HEALTH_SAFETY_MEETING = "healthSafetyMeeting",
  DANGEROUS_GOODS_REGISTER = "dangerousGoodsRegister",

  INCIDENT_REPORT_LIST = "incidentReportList",
  INCIDENT_REPORT_VIEW = "incidentReportView",
  INCIDENT_REPORT_EDIT = "incidentReportEdit",

  RISK_ASSESSMENT_LIST = "riskAssessmentList",
  RISK_ASSESSMENT_VIEW = "riskAssessmentView",
  RISK_ASSESSMENT_EDIT = "riskAssessmentEdit",

  HEALTH_SAFETY_MEETING_LIST = "healthSafetyMeetingList",
  HEALTH_SAFETY_MEETING_VIEW = "healthSafetyMeetingView",
  HEALTH_SAFETY_MEETING_EDIT = "healthSafetyMeetingEdit",

  DANGEROUS_GOODS_REGISTER_LIST = "dangerousGoodsRegisterList",
  DANGEROUS_GOODS_REGISTER_VIEW = "dangerousGoodsRegisterView",
  DANGEROUS_GOODS_REGISTER_EDIT = "dangerousGoodsRegisterEdit",

  // Company Document Register
  COMPANY_DOCUMENT_REGISTER_DASHBOARD = "company-document-register-dashboard",
  DOCUMENT_REGISTER = "documentRegister",
  COMPANY_PLAN = "companyPlan",
  COMPANY_DOCUMENTS = "companyDocuments",
  CUSTOM_FORMS = "customForms",

  // Crew
  CREW_DASHBOARD = "crew-dashboard",
  CREW_PARTICULARS = "crewParticulars",
  CREW_PARTICULARS_LIST = "crewParticularsList",
  CREW_PARTICULARS_EDIT = "crewParticularsEdit",

  CREW_PARTICULARS_VIEW = "crewParticularsView",
  CREW_PARTICULARS_VIEW_PROFILE = "crewParticularsViewProfile",
  CREW_PARTICULARS_VIEW_FORMS = "crewParticularsViewForms",
  CREW_PARTICULARS_VIEW_SEATIME = "crewParticularsViewSeaTime",
  CREW_PARTICULARS_VIEW_CERTIFICATES = "crewParticularsViewCertificates",
  CREW_PARTICULARS_VIEW_DRILLS = "crewParticularsViewDrills",
  CREW_PARTICULARS_VIEW_TRAINING = "crewParticularsViewTraining",

  CREW_CERTIFICATES = "crewCertificates",
  CREW_CERTIFICATES_VIEW = "viewCrewCertificates",

  //Other routes
  ACTION_LOG = "actionLog",
  SUPPORT = "support",
  PROFILE = "profile",
  VOYAGE = "voyage",

  VESSEL_STATUS = "status",
  ENGINE_HOURS = "engineHours",
  FUEL = "fuel",
  SURVEY_STATUS = "surveyStatus",
}

/**
 * List of all the routes and their configurations
 */
export const ROUTES_CONFIG: RoutesConfig = {
  // Main Stack Navigation
  [Routes.INDEX]: {
    name: Routes.INDEX,
    title: Routes.INDEX,
    path: "/",
  },
  [Routes.AUTH]: {
    name: Routes.AUTH,
    title: Routes.AUTH,
    path: `/${Routes.AUTH}`,
  },
  [Routes.HOME]: {
    name: Routes.HOME,
    title: Routes.HOME,
    path: `/${Routes.HOME}`,
  },

  [Routes.VESSEL]: {
    name: Routes.VESSEL,
    title: "Vessel",
  },
  [Routes.MAINTENANCE]: {
    name: Routes.MAINTENANCE,
    title: "Maintenance",
  },
  [Routes.SAFETY]: {
    name: Routes.SAFETY,
    title: "Safety",
  },
  [Routes.VESSEL_DOCUMENT_REGISTER]: {
    name: Routes.VESSEL_DOCUMENT_REGISTER,
    title: "Vessel Document Register",
  },
  [Routes.CREW]: {
    name: Routes.CREW,
    title: "Crew",
  },
  [Routes.HEALTH_SAFETY]: {
    name: Routes.HEALTH_SAFETY,
    title: "Health & Safety",
  },
  // [Routes.HEALTH_SAFETY]: {
  //   name: Routes.HEALTH_SAFETY,
  //   title: "Health & Safety",
  // },

  // Public routes - general
  [Routes.NETWORK]: {
    name: Routes.NETWORK,
    title: Routes.NETWORK,
    path: `/(public)/${Routes.NETWORK}`,
  },

  // ADMIN - Navigation
  [Routes.LOGIN]: {
    name: Routes.LOGIN,
    title: Routes.LOGIN,
    path: `/${Routes.AUTH}/${Routes.LOGIN}`,
  },
  [Routes.LOGOUT]: {
    name: Routes.LOGOUT,
    title: "Log out",
    path: `/${Routes.AUTH}/${Routes.LOGOUT}`,
    icon: { name: "logout" },
  },

  // AUTH - Navigation
  [Routes.ADMIN]: {
    name: Routes.ADMIN,
    title: "Admin Accounts",
    path: `/${Routes.AUTH}/${Routes.ADMIN}`,
  },

  // HOME - Drawer Navigation
  [Routes.FLEET_DASHBOARD]: {
    name: Routes.FLEET_DASHBOARD,
    title: "Fleet Dashboard",
    path: `/${Routes.HOME}/${Routes.FLEET_DASHBOARD}`,
    icon: { name: "anchor" },
  },

  [Routes.VESSEL_DASHBOARD]: {
    name: Routes.VESSEL_DASHBOARD,
    title: "Dashboard",
    path: `/${Routes.HOME}/vessel/${Routes.VESSEL_DASHBOARD}`,
    icon: { name: "directions_boat_filled" },
  },

  [Routes.LOGBOOK]: {
    name: Routes.LOGBOOK,
    title: "Logbook",
    path: `/${Routes.HOME}/vessel/${Routes.LOGBOOK}`,
    icon: { name: "menu_book" },
  },

  // Safety Section
  [Routes.SAFETY_DASHBOARD]: {
    name: Routes.SAFETY_DASHBOARD,
    title: "Safety",
    path: `/${Routes.HOME}/vessel/safety/${Routes.SAFETY_DASHBOARD}`,
    icon: { name: "support" },
    children: {
      [Routes.SAFETY_EQUIPMENT_CHECKS]: {},
      [Routes.SAFETY_EQUIPMENT_EXPIRIES]: {},
      [Routes.DRILLS]: {},
    },
  },
  [Routes.SAFETY_EQUIPMENT_CHECKS]: {
    name: Routes.SAFETY_EQUIPMENT_CHECKS,
    title: "Safety Checks",
    path: `/vessel/safety/${Routes.SAFETY_EQUIPMENT_CHECKS}`,
  },
  [Routes.SAFETY_EQUIPMENT_CHECKS_VIEW]: {
    name: Routes.SAFETY_EQUIPMENT_CHECKS_VIEW,
    title: "Safety Check - View",
    path: `/vessel/safety/${Routes.SAFETY_EQUIPMENT_CHECKS}/view`,
  },
  [Routes.SAFETY_EQUIPMENT_CHECKS_LIST]: {
    name: Routes.SAFETY_EQUIPMENT_CHECKS_LIST,
    title: "Safety Checks",
    path: `/${Routes.HOME}/vessel/safety/${Routes.SAFETY_EQUIPMENT_CHECKS}/list`,
  },

  [Routes.SAFETY_EQUIPMENT_EXPIRIES]: {
    name: Routes.SAFETY_EQUIPMENT_EXPIRIES,
    title: "Safety Equipment Expiries",
    path: `/vessel/safety/${Routes.SAFETY_EQUIPMENT_EXPIRIES}`,
  },
  [Routes.SAFETY_EQUIPMENT_EXPIRIES_VIEW]: {
    name: Routes.SAFETY_EQUIPMENT_EXPIRIES_VIEW,
    title: "Safety Equipment Expiries - View",
    path: `/vessel/safety/${Routes.SAFETY_EQUIPMENT_EXPIRIES}/view`,
  },
  [Routes.DRILLS]: {
    name: Routes.DRILLS,
    title: "Drills",
    path: `/vessel/safety/${Routes.DRILLS}`,
  },
  [Routes.DRILLS_VIEW_DRILLS_HISTORY]: {
    name: Routes.DRILLS_VIEW_DRILLS_HISTORY,
    title: "Drills History",
    path: `/vessel/safety/${Routes.DRILLS}/history`,
  },
  [Routes.DRILLS_VIEW_USER_DRILLS]: {
    name: Routes.DRILLS_VIEW_USER_DRILLS,
    title: "User Drills",
    path: `/vessel/safety/${Routes.DRILLS}/user`,
  },
  [Routes.DRILLS_VIEW_USER_DRILL]: {
    name: Routes.DRILLS_VIEW_USER_DRILL,
    title: "User Drills",
    path: `/vessel/safety/${Routes.DRILLS}/user-drill`,
  },
  [Routes.DRILLS_VIEW_DRILL]: {
    name: Routes.DRILLS_VIEW_DRILL,
    title: "User Drills",
    path: `/vessel/safety/${Routes.DRILLS}/drill`,
  },
  [Routes.DRILLS_VIEW_DRILL_REPORT]: {
    name: Routes.DRILLS_VIEW_DRILL_REPORT,
    title: "User Drills",
    path: `/vessel/safety/${Routes.DRILLS}/drill-report`,
  },

  // Maintenance Section
  [Routes.MAINTENANCE_DASHBOARD]: {
    name: Routes.MAINTENANCE_DASHBOARD,
    title: "Maintenance",
    path: `/vessel/maintenance/${Routes.MAINTENANCE_DASHBOARD}`,
    icon: { name: "build" },
    children: {
      [Routes.MAINTENANCE_SCHEDULE]: {
        // name: Routes.MAINTENANCE_SCHEDULE,
        // title: "Usage: If necessary REPLACE WITH CUSTOM NAME FROM THIS ROUTE",
      },
      [Routes.JOBLIST]: {},
      [Routes.SPARE_PARTS_LIST]: {},
      [Routes.EQUIPMENT_LIST]: {},
      [Routes.EQUIPMENT_MANUALS]: {},
      [Routes.MAINTENANCE_HISTORY]: {},
    },
  },
  [Routes.MAINTENANCE_SCHEDULE]: {
    name: Routes.MAINTENANCE_SCHEDULE,
    title: "Maintenance Schedule",
    path: `/vessel/maintenance/${Routes.MAINTENANCE_SCHEDULE}`,
  },
  [Routes.MAINTENANCE_SCHEDULE_VIEW]: {
    name: Routes.MAINTENANCE_SCHEDULE_VIEW,
    title: "Maintenance Schedule - View",
    path: `/vessel/maintenance/${Routes.MAINTENANCE_SCHEDULE}/view`,
  },

  [Routes.JOBLIST]: {
    name: Routes.JOBLIST,
    title: "Job List",
    path: `/vessel/maintenance/${Routes.JOBLIST}`,
  },
  [Routes.JOBLIST_VIEW]: {
    name: Routes.JOBLIST_VIEW,
    title: "Job List - View",
    path: `/vessel/maintenance/${Routes.JOBLIST}/view`,
  },

  [Routes.SPARE_PARTS_LIST]: {
    name: Routes.SPARE_PARTS_LIST,
    title: "Spare Parts List",
    path: `/vessel/maintenance/${Routes.SPARE_PARTS_LIST}`,
  },
  [Routes.SPARE_PARTS_LIST_VIEW]: {
    name: Routes.SPARE_PARTS_LIST_VIEW,
    title: "Spare Parts List - View",
    path: `/vessel/maintenance/${Routes.SPARE_PARTS_LIST}/view`,
  },

  [Routes.EQUIPMENT_LIST]: {
    name: Routes.EQUIPMENT_LIST,
    title: "Equipment List",
    path: `/vessel/maintenance/${Routes.EQUIPMENT_LIST}`,
  },
  [Routes.EQUIPMENT_LIST_VIEW]: {
    name: Routes.EQUIPMENT_LIST_VIEW,
    title: "Equipment List",
    path: `/vessel/maintenance/${Routes.EQUIPMENT_LIST}/view`,
  },

  [Routes.EQUIPMENT_MANUALS]: {
    name: Routes.EQUIPMENT_MANUALS,
    title: "Equipment Manuals",
    path: `/vessel/maintenance/${Routes.EQUIPMENT_MANUALS}`,
  },
  [Routes.EQUIPMENT_MANUALS_VIEW]: {
    name: Routes.EQUIPMENT_MANUALS_VIEW,
    title: "Equipment Manuals - View",
    path: `/vessel/maintenance/${Routes.EQUIPMENT_MANUALS}/view`,
  },
  [Routes.MAINTENANCE_HISTORY]: {
    name: Routes.MAINTENANCE_HISTORY,
    title: "Maintenance History",
    path: `/vessel/maintenance/${Routes.MAINTENANCE_HISTORY}`,
  },
  [Routes.MAINTENANCE_HISTORY_VIEW]: {
    name: Routes.MAINTENANCE_HISTORY_VIEW,
    title: "Maintenance History - View",
    path: `/vessel/maintenance/${Routes.MAINTENANCE_HISTORY}/view`,
  },

  // Vessel Document Register Section
  [Routes.VESSEL_DOCUMENT_REGISTER_DASHBOARD]: {
    name: Routes.VESSEL_DOCUMENT_REGISTER_DASHBOARD,
    title: "Vessel Document Register",
    path: `/vessel/vessel-document-register/${Routes.VESSEL_DOCUMENT_REGISTER_DASHBOARD}`,
    icon: { name: "description" },
    children: {
      [Routes.VESSEL_CERTIFICATES]: {},
      [Routes.VESSEL_DOCUMENTS]: {},
      [Routes.SURVEY_DOCUMENTS]: {},
      [Routes.STANDARD_OPERATING_PROCEDURES]: {},
    },
  },
  [Routes.VESSEL_CERTIFICATES]: {
    name: Routes.VESSEL_CERTIFICATES,
    title: "Vessel Certificates",
    path: `/vessel/vessel-document-register/${Routes.VESSEL_CERTIFICATES}`,
  },
  [Routes.VESSEL_CERTIFICATES_VIEW]: {
    name: Routes.VESSEL_CERTIFICATES_VIEW,
    title: "Vessel Certificates - View",
    path: `/vessel/vessel-document-register/${Routes.VESSEL_CERTIFICATES}/view`,
  },
  [Routes.VESSEL_DOCUMENTS]: {
    name: Routes.VESSEL_DOCUMENTS,
    title: "Vessel Documents",
    path: `/vessel/vessel-document-register/${Routes.VESSEL_DOCUMENTS}`,
  },
  [Routes.VESSEL_DOCUMENTS_VIEW]: {
    name: Routes.VESSEL_DOCUMENTS,
    title: "Vessel Documents",
    path: `/vessel/vessel-document-register/${Routes.VESSEL_DOCUMENTS}/view`,
  },
  [Routes.SURVEY_DOCUMENTS]: {
    name: Routes.SURVEY_DOCUMENTS,
    title: "Survey Documents",
    path: `/vessel/vessel-document-register/${Routes.SURVEY_DOCUMENTS}`,
  },
  [Routes.STANDARD_OPERATING_PROCEDURES]: {
    name: Routes.STANDARD_OPERATING_PROCEDURES,
    title: "Standard Operating Procedures",
    path: `/vessel/vessel-document-register/${Routes.STANDARD_OPERATING_PROCEDURES}`,
  },

  // Health and Safety Section
  [Routes.HEALTH_SAFETY_DASHBOARD]: {
    name: Routes.HEALTH_SAFETY_DASHBOARD,
    title: "Health & Safety",
    path: `/healthSafety/${Routes.HEALTH_SAFETY_DASHBOARD}`,
    icon: { name: "health_and_safety" },
    children: {
      [Routes.INCIDENT_REPORT]: {},
      [Routes.RISK_ASSESSMENT]: {},
      [Routes.HEALTH_SAFETY_MEETING]: {},
      [Routes.DANGEROUS_GOODS_REGISTER]: {},
    },
  },
  [Routes.INCIDENT_REPORT]: {
    name: Routes.INCIDENT_REPORT,
    title: "Incident / Event Reports",
    path: `/healthSafety/${Routes.INCIDENT_REPORT}`,
    children: {
      [Routes.INCIDENT_REPORT_LIST]: {},
      [Routes.INCIDENT_REPORT_VIEW]: {},
      [Routes.INCIDENT_REPORT_EDIT]: {},
    },
  },
  [Routes.INCIDENT_REPORT_LIST]: {
    name: Routes.INCIDENT_REPORT_LIST,
    title: "Incident / Event Reports",
    path: `/healthSafety/${Routes.INCIDENT_REPORT}`,
  },
  [Routes.INCIDENT_REPORT_VIEW]: {
    name: Routes.INCIDENT_REPORT_VIEW,
    title: "Incident / Event Reports - View",
    path: `/healthSafety/${Routes.INCIDENT_REPORT}/view`,
  },
  [Routes.INCIDENT_REPORT_EDIT]: {
    name: Routes.INCIDENT_REPORT_EDIT,
    title: "Incident / Event Reports - Edit",
    path: `/healthSafety/${Routes.INCIDENT_REPORT}/edit`,
  },

  [Routes.RISK_ASSESSMENT]: {
    name: Routes.RISK_ASSESSMENT,
    title: "Risk Assessments",
    path: `/healthSafety/${Routes.RISK_ASSESSMENT}`,
    children: {
      [Routes.RISK_ASSESSMENT_LIST]: {},
      [Routes.RISK_ASSESSMENT_VIEW]: {},
      [Routes.RISK_ASSESSMENT_EDIT]: {},
    },
  },
  [Routes.RISK_ASSESSMENT_LIST]: {
    name: Routes.RISK_ASSESSMENT_LIST,
    title: "Risk Assessments",
    path: `/healthSafety/${Routes.RISK_ASSESSMENT}`,
  },
  [Routes.RISK_ASSESSMENT_VIEW]: {
    name: Routes.RISK_ASSESSMENT_VIEW,
    title: "Risk Assessments - View",
    path: `/healthSafety/${Routes.RISK_ASSESSMENT}/view`,
  },
  [Routes.RISK_ASSESSMENT_EDIT]: {
    name: Routes.RISK_ASSESSMENT_EDIT,
    title: "Risk Assessments - Edit",
    path: `/healthSafety/${Routes.RISK_ASSESSMENT}/edit`,
  },

  [Routes.HEALTH_SAFETY_MEETING]: {
    name: Routes.HEALTH_SAFETY_MEETING,
    title: "Health & Safety Meetings",
    path: `/healthSafety/${Routes.HEALTH_SAFETY_MEETING}`,
    children: {
      [Routes.HEALTH_SAFETY_MEETING_LIST]: {},
      [Routes.HEALTH_SAFETY_MEETING_VIEW]: {},
      [Routes.HEALTH_SAFETY_MEETING_EDIT]: {},
    },
  },
  [Routes.HEALTH_SAFETY_MEETING_LIST]: {
    name: Routes.HEALTH_SAFETY_MEETING_LIST,
    title: "Health & Safety Meetings",
    path: `/healthSafety/${Routes.HEALTH_SAFETY_MEETING}`,
  },
  [Routes.HEALTH_SAFETY_MEETING_VIEW]: {
    name: Routes.HEALTH_SAFETY_MEETING_VIEW,
    title: "Health & Safety Meetings - View",
    path: `/healthSafety/${Routes.HEALTH_SAFETY_MEETING}/view`,
  },
  [Routes.HEALTH_SAFETY_MEETING_EDIT]: {
    name: Routes.HEALTH_SAFETY_MEETING_EDIT,
    title: "Health & Safety Meetings - Edit",
    path: `/healthSafety/${Routes.HEALTH_SAFETY_MEETING}/edit`,
  },

  [Routes.DANGEROUS_GOODS_REGISTER]: {
    name: Routes.DANGEROUS_GOODS_REGISTER,
    title: "Dangerous Goods Register",
    path: `/healthSafety/${Routes.DANGEROUS_GOODS_REGISTER}`,
    children: {
      [Routes.DANGEROUS_GOODS_REGISTER_LIST]: {},
      [Routes.DANGEROUS_GOODS_REGISTER_VIEW]: {},
      [Routes.DANGEROUS_GOODS_REGISTER_EDIT]: {},
    },
  },
  [Routes.DANGEROUS_GOODS_REGISTER_LIST]: {
    name: Routes.DANGEROUS_GOODS_REGISTER_LIST,
    title: "Dangerous Goods Register",
    path: `/healthSafety/${Routes.DANGEROUS_GOODS_REGISTER}`,
  },
  [Routes.DANGEROUS_GOODS_REGISTER_VIEW]: {
    name: Routes.DANGEROUS_GOODS_REGISTER_VIEW,
    title: "Dangerous Goods Register - View",
    path: `/healthSafety/${Routes.DANGEROUS_GOODS_REGISTER}/view`,
  },
  [Routes.DANGEROUS_GOODS_REGISTER_EDIT]: {
    name: Routes.DANGEROUS_GOODS_REGISTER_EDIT,
    title: "Dangerous Goods Register - Edit",
    path: `/healthSafety/${Routes.DANGEROUS_GOODS_REGISTER}/edit`,
  },

  // Company Document Register Section
  [Routes.DOCUMENT_REGISTER]: {
    // Parent Group Route
    name: Routes.DOCUMENT_REGISTER,
    title: "Company Document Register",
  },
  [Routes.COMPANY_DOCUMENT_REGISTER_DASHBOARD]: {
    name: Routes.COMPANY_DOCUMENT_REGISTER_DASHBOARD,
    title: "Company Document Register",
    path: `/documentRegister/${Routes.COMPANY_DOCUMENT_REGISTER_DASHBOARD}`,
    icon: { name: "file_copy" },
    children: {
      [Routes.COMPANY_PLAN]: {},
      [Routes.COMPANY_DOCUMENTS]: {},
      [Routes.CUSTOM_FORMS]: {},
    },
  },
  [Routes.COMPANY_PLAN]: {
    name: Routes.COMPANY_PLAN,
    title: "Company Plan",
    path: `/documentRegister/${Routes.COMPANY_PLAN}`,
  },
  [Routes.COMPANY_DOCUMENTS]: {
    name: Routes.COMPANY_DOCUMENTS,
    title: "Company Documents",
    path: `/documentRegister/${Routes.COMPANY_DOCUMENTS}`,
  },
  [Routes.CUSTOM_FORMS]: {
    name: Routes.CUSTOM_FORMS,
    title: "Forms / Checklists",
    path: `/documentRegister/${Routes.CUSTOM_FORMS}`,
  },

  // Crew Section
  [Routes.CREW_DASHBOARD]: {
    name: Routes.CREW_DASHBOARD,
    title: "Crew",
    path: `/crew/${Routes.CREW_DASHBOARD}`,
    icon: { name: "groups" },
    children: {
      [Routes.CREW_PARTICULARS_LIST]: {},
      [Routes.CREW_CERTIFICATES]: {},
    },
  },
  [Routes.CREW_PARTICULARS]: {
    name: Routes.CREW_PARTICULARS,
    children: {
      [Routes.CREW_PARTICULARS_LIST]: {},
      [Routes.CREW_PARTICULARS_VIEW]: {},
      [Routes.CREW_PARTICULARS_EDIT]: {},
    },
  },
  [Routes.CREW_PARTICULARS_LIST]: {
    name: Routes.CREW_PARTICULARS_LIST,
    title: "Crew Particulars",
    path: `/crew/${Routes.CREW_PARTICULARS}`,
  },
  [Routes.CREW_PARTICULARS_EDIT]: {
    name: Routes.CREW_PARTICULARS_EDIT,
    title: "Crew Particulars - Edit",
    path: `/crew/${Routes.CREW_PARTICULARS}/edit`,
  },

  [Routes.CREW_PARTICULARS_VIEW]: {
    name: Routes.CREW_PARTICULARS_VIEW,
    title: "Crew Particulars - View",
    path: `/crew/${Routes.CREW_PARTICULARS}/view`,
    children: {
      [Routes.CREW_PARTICULARS_VIEW_PROFILE]: {},
      [Routes.CREW_PARTICULARS_VIEW_FORMS]: {},
      [Routes.CREW_PARTICULARS_VIEW_SEATIME]: {},
      [Routes.CREW_PARTICULARS_VIEW_CERTIFICATES]: {},
      [Routes.CREW_PARTICULARS_VIEW_DRILLS]: {},
      [Routes.CREW_PARTICULARS_VIEW_TRAINING]: {},
    },
  },
  [Routes.CREW_PARTICULARS_VIEW_PROFILE]: {
    name: Routes.CREW_PARTICULARS_VIEW_PROFILE,
    title: "Profile",
    path: `/crew/${Routes.CREW_PARTICULARS}/view/profile`,
  },
  [Routes.CREW_PARTICULARS_VIEW_FORMS]: {
    name: Routes.CREW_PARTICULARS_VIEW_FORMS,
    title: "Forms / Documents",
    path: `/crew/${Routes.CREW_PARTICULARS}/view/forms`,
  },
  [Routes.CREW_PARTICULARS_VIEW_SEATIME]: {
    name: Routes.CREW_PARTICULARS_VIEW_SEATIME,
    title: "Sea Time",
    path: `/crew/${Routes.CREW_PARTICULARS}/view/seaTime`,
  },
  [Routes.CREW_PARTICULARS_VIEW_CERTIFICATES]: {
    name: Routes.CREW_PARTICULARS_VIEW_CERTIFICATES,
    title: "Certificates",
    path: `/crew/${Routes.CREW_PARTICULARS}/view/certificates`,
  },
  [Routes.CREW_PARTICULARS_VIEW_DRILLS]: {
    name: Routes.CREW_PARTICULARS_VIEW_DRILLS,
    title: "Drills",
    path: `/crew/${Routes.CREW_PARTICULARS}/view/drills`,
  },
  [Routes.CREW_PARTICULARS_VIEW_TRAINING]: {
    name: Routes.CREW_PARTICULARS_VIEW_TRAINING,
    title: "Training",
    path: `/crew/${Routes.CREW_PARTICULARS}/view/training`,
  },

  [Routes.CREW_CERTIFICATES]: {
    name: Routes.CREW_CERTIFICATES,
    title: "Crew Certificates",
    path: `/crew/${Routes.CREW_CERTIFICATES}`,
  },

  [Routes.CREW_CERTIFICATES_VIEW]: {
    name: Routes.CREW_CERTIFICATES_VIEW,
    title: "View Crew Certificates",
    path: `/crew/${Routes.CREW_CERTIFICATES}/view`,
  },

  // Other routes
  [Routes.ACTION_LOG]: {
    name: Routes.ACTION_LOG,
    title: "Action Log",
    path: `/${Routes.ACTION_LOG}`,
    icon: { name: "assignment" },
  },
  [Routes.SUPPORT]: {
    name: Routes.SUPPORT,
    title: "Support",
    path: `/${Routes.SUPPORT}`,
    icon: { name: "contact_support" },
  },
  [Routes.PROFILE]: {
    name: Routes.PROFILE,
    title: "Profile",
    path: `/${Routes.PROFILE}`,
  },
  [Routes.VOYAGE]: {
    name: Routes.VOYAGE,
    title: "Voyage",
    path: `/vessel/${Routes.VOYAGE}`,
  },

  [Routes.VESSEL_STATUS]: {
    name: Routes.VESSEL_STATUS,
    title: "Vessel Status",
    path: `/vessel/${Routes.VESSEL_STATUS}`,
  },
  [Routes.ENGINE_HOURS]: {
    name: Routes.ENGINE_HOURS,
    title: "Engine Hours",
    path: `/vessel/${Routes.ENGINE_HOURS}`,
  },
  [Routes.FUEL]: {
    name: Routes.FUEL,
    title: "Fuel",
    path: `/vessel/${Routes.FUEL}`,
  },
  [Routes.SURVEY_STATUS]: {
    name: Routes.SURVEY_STATUS,
    title: "Survey Status",
    path: `/vessel/${Routes.SURVEY_STATUS}`,
  },
};
