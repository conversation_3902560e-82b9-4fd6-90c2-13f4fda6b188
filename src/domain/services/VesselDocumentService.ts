import { injectable } from "inversify";
import {
  FirestoreOperation,
  FirestoreRecord,
} from "../data/FirestoreOperation";
import { UpdateVesselDocumentDto } from "../use-cases/vesselDocumentRegister/UpdateVesselDocumentUseCase";
import { DocRef } from "../data/IFirestoreService";

export interface IVesselDocumentService {
  updateVesselDocument(
    operation: FirestoreOperation,
    updateJobListDto: UpdateVesselDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  };

  //   createVesselCertificate(
  //     operation: FirestoreOperation,
  //     createVesselCertificateDto: CreateVesselCertificateDto,
  //     userId: string,
  //     licenseeId: string,
  //   ): {
  //     ref: DocRef;
  //     records: FirestoreRecord[];
  //   };

  //   archiveVesselCertificate(
  //     operation: FirestoreOperation,
  //     vesselCertificateId: string,
  //     userId: string,
  //   ): FirestoreRecord;

  //   renewVesselCertificate(
  //     operation: FirestoreOperation,
  //     dto: RenewVesselCertificateDto,
  //     userId: string,
  //     licenseeId: string,
  //   ): {
  //     ref: DocRef;
  //     records: FirestoreRecord[];
  //   };
}

@injectable()
export class VesselDocumentService implements IVesselDocumentService {
  updateVesselDocument(
    operation: FirestoreOperation,
    updateJobListDto: UpdateVesselDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  } {
    const {
      vesselId,
      id,
      dateExpires,
      emailReminder,
      categoryId,
      dateToRemind,
      sfdoc,
      files,
      ...rest
    } = updateJobListDto;

    const data = {
      ...rest,
      dateExpires: dateExpires ?? operation.deleteField(),
      emailReminder: emailReminder ?? operation.deleteField(),
      categoryId: categoryId ?? operation.deleteField(),
      dateToRemind: dateToRemind ?? operation.deleteField(),
      files: files ?? operation.deleteField(),
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    };

    const updatedVesselCertificateRecord = {
      ref: operation.makeRef("vesselDocuments", id),
      data,
      options: { merge: true },
    };

    //TODO Create a new category if it doesn't exist
    //TODO: Add Links

    const vesselTouched = this.vesselTouched(
      operation,
      "vesselDocuments",
      licenseeId,
      vesselId
    );

    return {
      ref: updatedVesselCertificateRecord.ref,
      records: [updatedVesselCertificateRecord, vesselTouched],
    };
  }

  private vesselTouched(
    operation: FirestoreOperation,
    collectionName: string,
    licenseeId: string,
    vesselId: string
  ) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    };

    const vesselTouched = {
      ref: operation.makeRef("whenVesselTouched", vesselId),
      data: vesselData,
      options: { merge: true },
    };

    return vesselTouched;
  }
}
