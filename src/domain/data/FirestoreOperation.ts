import {
  CollectionRef,
  DocRef,
  IFirestoreService,
  WriteBatch,
} from "@src/domain/data/IFirestoreService";
import { ILogger } from "@src/domain/util/ILogger";
import { deleteField } from "firebase/firestore";

const DEFAULT_BATCH_SIZE = 20;

export interface IDatastoreOperation<T> {}

export interface FirestoreRecord {
  ref: DocRef;
  data: any;
  // options?: any;
  options?: any & {
    merge: boolean;
  };
}

export type OperationType = "create" | "update";

export interface FirestoreOperationOptions {
  maximumBatchSize?: number;
  operationType: OperationType;
  operationDescription: string;
}

type TraceData = {
  ref: unknown;
  data: unknown;
};

export class FirestoreOperation
  implements IDatastoreOperation<FirestoreRecord>
{
  private readonly logger: ILogger;
  private readonly firestoreService;

  private readonly batches: WriteBatch[] = [];
  private readonly batchSize: number;

  // private readonly batchTrace: BatchTrace;
  private readonly operationType: OperationType;
  private readonly operationDescription: string;
  private readonly traceData: TraceData[] = [];

  /** Counter for number of records in current batch */
  private countInBatch = 0;

  private constructor(
    firestoreService: IFirestoreService,
    options: FirestoreOperationOptions,
    logger: ILogger,
  ) {
    this.logger = logger.scoped("FirestoreOperation");
    this.firestoreService = firestoreService;
    this.batchSize = options?.maximumBatchSize ?? DEFAULT_BATCH_SIZE;

    const batch = this.firestoreService.createWriteBatch();
    this.batches.push(batch);

    // this.batchTrace = makeBatchTrace(batch); // TODO - Need to support batch traces
    this.operationType = options.operationType;
    this.operationDescription = options.operationDescription;

    // this.batchTrace.exampleOperation = this.operationType;
  }

  public static create(
    service: IFirestoreService,
    options: FirestoreOperationOptions,
    logger: ILogger,
  ) {
    return new FirestoreOperation(service, options, logger);
  }

  public add(record: FirestoreRecord) {
    const batch = this.batch;

    try {
      // Let the underlying Firebase implementation handle the reference
      // without forcing type casts that might not be compatible
      if (record.options) {
        batch.set(record.ref, record.data, record.options);
      } else {
        batch.set(record.ref, record.data);
      }

      const trace = {
        ref: record.ref,
        data: record.data,
      };
      this.traceData.push(trace);

      this.countInBatch++;
    } catch (error) {
      this.logger.error("Error adding record to batch", {
        error,
        recordRef: record.ref,
        recordData: record.data,
      });
      throw error;
    }

    return this;
  }

  public addMany(records: FirestoreRecord[]) {
    records.forEach((record) => this.add(record));
    return this;
  }

  public async commit() {
    // this.batchTrace.data = this.traceData;
    // this.batchTrace.save(this.operationDescription);

    const promises = this.batches.map((batch) => {
      this.logger.info("Committing batch", { batch });
      return batch.commit();
    });

    try {
      await Promise.all(promises);
      this.logger.info(
        "Operation committed successfully: " + this.operationDescription,
      );
      // this.batchTrace.reportSuccess(); // TODO
    } catch (error) {
      const err = error as Error;
      this.logger.error("Error committing Operation: ", err);
      // this.batchTrace.reportError(err.message, err); // TODO
    }
  }

  public serverTimestamp = () => this.firestoreService.serverTimestamp();

  public deleteField = () => this.firestoreService.deleteField();

  private get batch() {
    if (this.countInBatch >= this.batchSize) {
      this.batches.push(this.firestoreService.createWriteBatch());
      this.countInBatch = 0;
    }

    return this.batches[this.batches.length - 1];
  }

  public makeRef(collectionName: string, docId?: string): DocRef {
    return docId
      ? (this.makeDocRef(collectionName, docId) as DocRef)
      : (this.makeCollectionRef(collectionName) as DocRef);
  }

  private makeCollectionRef(collectionName: string) {
    return this.firestoreService.createCollectionRef(collectionName);
  }

  private makeDocRef(collectionName: string, docId: string) {
    return this.firestoreService.createDocumentRef(collectionName, docId);
  }

  public arrayUnion = (...elements: unknown[]) => {
    return this.firestoreService.arrayUnion(...elements);
  };

  public arrayRemove = (...elements: unknown[]) => {
    return this.firestoreService.arrayRemove(...elements);
  };
}

export const deleteValue = deleteField();
